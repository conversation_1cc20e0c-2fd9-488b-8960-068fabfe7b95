locals {
  nue_terraform_app_remote_state_address = "https://gitlab.com/api/v4/projects/${var.nue_terraform_project.id}/terraform/state/${var.nue_terraform_project.name}-${var.env}"
}


data "terraform_remote_state" "nue_terraform" {
  backend = "http"

  config = {
    address  = local.nue_terraform_app_remote_state_address
    username = var.gitlab_username
    password = var.gitlab_access_token
  }
} 