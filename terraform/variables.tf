variable "env" {
  default = ""
}

variable "region" {
  default = "us-east-1"
}

variable "tags" {
  description = "Tags for the infrastructure"
  type        = map(string)
  default     = {}
}

variable "app_name" {
  default = "pdf"
}

variable "image_version" {
  default = ""
}

variable "container_port" {
  default = 8080
}

variable "splunk_hec_token" {
  default = ""
}

variable "gitlab_registry" {
  default = ""
}



variable "cpu_unit" {
  description = "CPU unit for task"
  type        = number
  default     = 1024
}

variable "memory_unit" {
  description = "Memory unit for task"
  type        = number
  default     = 2048
}


variable "task_cpu_unit" {
  description = "CPU unit for the task"
  type        = number
  default     = 1024
}

variable "task_memory_unit" {
  description = "Memory unit for the task"
  type        = number
  default     = 2048
}

# Spring Profiles
variable "active_spring_profiles" {
  description = "Spring active profiles"
  type        = string
  default     = null
}



variable "container_memory_hard_limit" {
  description = "Memory hard limit for ECS task"
  type        = number
  default     = 1024
}

variable "container_memory_soft_limit" {
  description = "Memory soft limit for ECS task"
  type        = number
  default     = 512
}

variable "gitlab_username" {
  type        = string
  description = "GitLab username to query remote state"
  default     = "gitlab-ci-token"
}

variable "gitlab_access_token" {
  type        = string
  description = "GitLab access token to query remote state"
  sensitive   = true
}

variable "nue_terraform_project" {
  type        = map(string)
  description = "GitLab remote state project for Nue Terraform"
  default = {
    id   = "41804496"
    name = "nue-terraform"
  }
}


variable "min_capacity" {
  description = "ECS Autoscale minimum container capacity"
  default     = 1
}

variable "max_capacity" {
  description = "ECS Autoscale maximum container capacity"
  default     = 1
}

variable "target_value" {
  description = "ECS Target tracking target value percentage"
  default     = 40
}

variable "desired_container_count" {
  description = "The desired amount of tasks running for the service"
  default     = 1
}


variable "otlp_endpoint" {
  description = "URL of OTLP endpoint"
  type        = string
  default     = "https://in-otel.hyperdx.io"
}

variable "otlp_api_key" {
  description = "API Key for OTLP endpoint"
  type        = string
  default     = ""
  sensitive   = true
}

variable "otel_metrics_exporter" {
  description = "Metrics exporter config"
  type        = string
  default     = "none"
}

variable "otel_traces_exporter" {
  description = "Traces exporter config"
  type        = string
  default     = "none"
}

variable "java_tool_opts" {
  description = "Java tool options"
  type        = string
  default     = ""
}

variable "otel_java_extension" {
  description = "Nue Opentelemetry Java Extensions"
  type        = string
  default     = ""
}

variable "otel_logs_exporter" {
  description = "Log exporter config"
  type        = string
  default     = "none"
}

variable "browser_logging" {
  description = "Enable browser logging"
  type        = string
  default     = "false"
}

variable "browser_numbers" {
  description = "values for browser logging"
  type        = string
  default     = 2
}