
locals {

  private_subnet_ids = data.terraform_remote_state.nue_terraform.outputs.vpc.private_subnets
  vpc_id             = data.terraform_remote_state.nue_terraform.outputs.vpc.id
  cluster_arn        = data.terraform_remote_state.nue_terraform.outputs.cluster_arn
  route53_zone_id    = values(data.terraform_remote_state.nue_terraform.outputs.route53_zone.this_route53_zone_zone_id)
  container_name     = "${var.env}-${var.app_name}"
  tags               = var.tags
  otel_api_key_arn   = data.terraform_remote_state.nue_terraform.outputs.ssm_otel_api_key_arn
  otel_kms_key_arn   = data.terraform_remote_state.nue_terraform.outputs.kms_otel_api_key_arn
  gitlab_secrets     = data.terraform_remote_state.nue_terraform.outputs.gitlab_secret_arn
  gitlab_secrets_kms = data.terraform_remote_state.nue_terraform.outputs.gitlab_secret_kms_arn
  auth_app           = data.terraform_remote_state.nue_terraform.outputs.app_endpoints.auth_app

  app_name = "${var.env}-pdf-application"

  pdf_task_environment = {
    "AUTH_APP_ENDPOINT"      = data.terraform_remote_state.nue_terraform.outputs.app_endpoints.auth_app
    "APP_ROOT_URL"           = var.env == "dev" ? "https://app.nue.io" : "https://app.${var.env}.nue.io"
    "SPRING_PROFILES_ACTIVE" = var.env == "dev" ? "prod" : "${var.env}"

    "OTEL_EXPORTER_OTLP_ENDPOINT" = var.otlp_endpoint
    "OTEL_EXPORTER_OTLP_HEADERS"  = var.otlp_api_key
    "OTEL_SERVICE_NAME"           = "${var.env}-${var.app_name}"
    "OTEL_RESOURCE_ATTRIBUTES"    = "deployment.environment=${var.env}"
    "OTEL_LOGS_EXPORTER"          = var.otel_logs_exporter
    "OTEL_METRICS_EXPORTER"       = var.otel_metrics_exporter
    "OTEL_TRACES_EXPORTER"        = var.otel_traces_exporter
    "OTEL_JAVAAGENT_EXTENSIONS"   = var.otel_java_extension
    "JAVA_TOOL_OPTIONS"           = var.java_tool_opts
    "BROWSER_LOGGING"             = var.browser_logging
    "BROWSER_NUMBERS"             = var.browser_numbers
  }

}

data "aws_region" "current" {}
data "aws_caller_identity" "current" {}

data "aws_vpc" "vpc_id" {
  id = local.vpc_id
}



module "pdf_service" {
  source  = "gitlab.com/nue-apps/terraform-ecs/aws"
  version = "1.14.1"

  env                         = var.env
  app_name                    = var.app_name
  image_version               = var.image_version
  container_port              = var.container_port
  task_environment            = local.pdf_task_environment
  splunk_hec_token            = var.splunk_hec_token
  cluster_arn                 = local.cluster_arn
  alb_tg_arn                  = module.integration_private_alb.target_groups.instance.arn
  private_subnet_ids          = local.private_subnet_ids
  vpc_id                      = local.vpc_id
  repository_url              = var.gitlab_registry
  task_role_arn               = module.ecs_task_role.aws_iam_role_ecs_task_execution_role_arn
  container_memory_hard_limit = var.container_memory_hard_limit
  container_memory_soft_limit = var.container_memory_soft_limit
  task_cpu_unit               = var.task_cpu_unit
  task_memory_unit            = var.task_memory_unit
  gitlab_secret_arn           = local.gitlab_secrets
  min_capacity                = var.min_capacity
  max_capacity                = var.max_capacity
  target_value                = var.target_value
  ecs_app_name                = "-${var.app_name}"
  desired_container_count     = var.desired_container_count
  ssm_otel_api_key_arn        = local.otel_api_key_arn
  task_secrets                = []
}

module "ecs_task_role" {
  source  = "gitlab.com/nue-apps/terraform-aws-ecs-task-role/aws"
  version = "1.0.1"
  name    = "${var.env}_pdf_application"
}


module "security_group" {
  source  = "terraform-aws-modules/security-group/aws"
  version = "~> 5.1.2"
  name    = "${var.env}_pdf_application_sg"
  vpc_id  = local.vpc_id

  ingress_cidr_blocks = [
  data.aws_vpc.vpc_id.cidr_block]

  ingress_with_cidr_blocks = [
    {
      from_port   = 5000
      to_port     = 6000
      protocol    = "tcp"
      description = "Cluster Ingress"
      cidr_blocks = "10.0.0.0/16"
    },
    {
      from_port   = 8
      to_port     = 0
      protocol    = "icmp"
      description = "Cluster Ingress - icmp"
      cidr_blocks = "10.0.0.0/16"
    },
    {
      from_port   = 80
      to_port     = 80
      protocol    = "tcp"
      description = "Cluster Ingress - gateway"
      cidr_blocks = "10.0.0.0/16"
    },
    {
      from_port   = 443
      to_port     = 443
      protocol    = "tcp"
      description = "Cluster Ingress - gateway"
      cidr_blocks = "10.0.0.0/16"
    },
    {
      from_port   = 8080
      to_port     = 8080
      protocol    = "tcp"
      description = "Cluster Ingress"
      cidr_blocks = "10.0.0.0/16"
    }
  ]

  egress_with_cidr_blocks = [
    {
      from_port   = 0
      to_port     = 0
      protocol    = "-1"
      cidr_blocks = "0.0.0.0/0"
    }
  ]

  tags = {
    Environment = var.env
  }
}


module "integration_private_alb" {
  source  = "terraform-aws-modules/alb/aws"
  version = "~> 9.11.0"

  name               = "${var.env}-pdf-application-alb"
  load_balancer_type = "application"
  internal           = true

  vpc_id = local.vpc_id

  security_groups = [module.security_group.security_group_id]
  subnets         = local.private_subnet_ids

  listeners = {
    http = {
      port     = 80
      protocol = "HTTP"
      forward = {
        target_group_key = "instance"
      }
    }
  }

  target_groups = {
    instance = {
      name_prefix          = trimsuffix(substr("${var.env}", 0, 6), " ")
      backend_protocol     = "HTTP"
      backend_port         = var.container_port
      target_type          = "ip"
      deregistration_delay = 10
      create_attachment    = "false"
      health_check = {
        enabled             = true
        interval            = 30
        path                = "/health"
        port                = "traffic-port"
        healthy_threshold   = 3
        unhealthy_threshold = 3
        timeout             = 6
        protocol            = "HTTP"
        matcher             = "200-399"
      }
      tags = var.tags
    }
  }

}

data "aws_route53_zone" "route53" {
  zone_id = local.route53_zone_id[0]
}

module "private_route_record" {
  source  = "terraform-aws-modules/route53/aws//modules/records"
  version = "~> 3.1.0"

  zone_name    = data.aws_route53_zone.route53.name
  private_zone = true

  records = [
    {
      name = "${var.env}-pdf-application-app"
      type = "A"
      alias = {
        name                   = module.integration_private_alb.dns_name
        zone_id                = module.integration_private_alb.zone_id
        evaluate_target_health = true
      }
    }
  ]
}


data "aws_iam_policy_document" "iam_policy_doc" {

  statement {
    effect    = "Allow"
    actions   = ["secretsmanager:GetSecretValue"]
    resources = ["${local.gitlab_secrets}"]
  }
  statement {
    effect    = "Allow"
    actions   = ["kms:Decrypt"]
    resources = ["${local.gitlab_secrets_kms}"]
  }
  statement {
    effect    = "Allow"
    actions   = ["kms:Decrypt"]
    resources = ["${local.otel_kms_key_arn}"]
  }
  statement {
    effect    = "Allow"
    actions   = ["secretsmanager:GetSecretValue"]
    resources = ["arn:aws:secretsmanager:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:secret:nuecommon-*"]
  }
}

resource "aws_iam_policy" "iam_policy" {
  name   = "${local.app_name}-ssm"
  policy = data.aws_iam_policy_document.iam_policy_doc.json
}

resource "aws_iam_role_policy_attachment" "policy_attachment" {
  role       = module.ecs_task_role.aws_iam_role_ecs_task_execution_role_name
  policy_arn = aws_iam_policy.iam_policy.arn
}