#!/bin/sh

# Attempt to start the Java application
java -XX:-OmitStackTraceInFastThrow -jar /app/pdf-application-0.0.1-SNAPSHOT.jar &

# Capture the PID of the Java process
JAVA_PID=$!

# Function to check if the Java process is running
is_java_running() {
    ps -p $JAVA_PID > /dev/null 2>&1
}

# Wait for a short time to see if Java starts successfully
sleep 5

# Check if Java is running, if not, log the error
if ! is_java_running; then
    echo "Java application failed to start. Container will continue running for debugging."
fi

# Keep the container running indefinitely
while true; do
    sleep 60
    if is_java_running; then
        echo "Java application is still running."
    else
        echo "Java application has stopped."
    fi
done
