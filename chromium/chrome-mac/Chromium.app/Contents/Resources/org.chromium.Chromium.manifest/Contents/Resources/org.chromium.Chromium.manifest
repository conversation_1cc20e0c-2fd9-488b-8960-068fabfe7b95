<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>pfm_description</key>
	<string></string>
	<key>pfm_domain</key>
	<string>org.chromium.Chromium</string>
	<key>pfm_name</key>
	<string>Google_Chrome</string>
	<key>pfm_subkeys</key>
	<array>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AbusiveExperienceInterventionEnforce</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AccessCodeCastDeviceDuration</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AccessCodeCastEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AccessControlAllowMethodsInCORSPreflightSpecConformant</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AccessibilityImageLabelsEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AdditionalDnsQueryTypesEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AdsSettingForIntrusiveAdsSites</string>
			<key>pfm_range_list</key>
			<array>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AdvancedProtectionAllowed</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AllHttpAuthSchemesAllowedForOrigins</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AllowBackForwardCacheForCacheControlNoStorePageEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AllowCrossOriginAuthPrompt</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AllowDeletingBrowserHistory</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AllowDinosaurEasterEgg</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AllowFileSelectionDialogs</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AllowWebAuthnWithBrokenTlsCerts</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AllowedDomainsForApps</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AlternateErrorPagesEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AlternativeBrowserParameters</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AlternativeBrowserPath</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AlwaysOpenPdfExternally</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AmbientAuthenticationInPrivateModesEnabled</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
				<integer>3</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AudioCaptureAllowed</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AudioCaptureAllowedUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AudioSandboxEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AuthNegotiateDelegateAllowlist</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AuthNegotiateDelegateByKdcPolicy</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AuthSchemes</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AuthServerAllowlist</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AutoLaunchProtocolsFromOrigins</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>dictionary</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AutoOpenAllowedForURLs</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AutoOpenFileTypes</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AutoSelectCertificateForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AutofillAddressEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AutofillCreditCardEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AutomaticFullscreenAllowedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AutomaticFullscreenBlockedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AutoplayAllowed</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>AutoplayAllowlist</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BasicAuthOverHttpEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BatterySaverModeAvailability</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BeforeunloadEventCancelByPreventDefaultEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BlockExternalExtensions</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BlockThirdPartyCookies</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BookmarkBarEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BrowserAddPersonEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BrowserGuestModeEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BrowserGuestModeEnforced</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BrowserLabsEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BrowserNetworkTimeQueriesEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BrowserSignin</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BrowserSwitcherDelay</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BrowserSwitcherEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BrowserSwitcherExternalGreylistUrl</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BrowserSwitcherExternalSitelistUrl</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BrowserSwitcherKeepLastChromeTab</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BrowserSwitcherParsingMode</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BrowserSwitcherUrlGreylist</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BrowserSwitcherUrlList</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BrowserThemeColor</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BrowsingDataLifetime</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>dictionary</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>BuiltInDnsClientEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>CORSNonWildcardRequestHeadersSupport</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>CertificateTransparencyEnforcementDisabledForCas</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>CertificateTransparencyEnforcementDisabledForLegacyCas</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>CertificateTransparencyEnforcementDisabledForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ChromeForTestingAllowed</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ChromeVariations</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ClearBrowsingDataOnExitList</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ClickToCallEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ClipboardAllowedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ClipboardBlockedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>CloudManagementEnrollmentMandatory</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>CloudManagementEnrollmentToken</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>CloudPolicyOverridesPlatformPolicy</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>CloudPrintProxyEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>CloudUserPolicyMerge</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>CloudUserPolicyOverridesCloudMachinePolicy</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>CommandLineFlagSecurityWarningsEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ComponentUpdatesEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>CompressionDictionaryTransportEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>CookiesAllowedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>CookiesBlockedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>CookiesSessionOnlyForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>CreatePasskeysInICloudKeychain</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>CreateThemesSettings</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DNSInterceptionChecksEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DataUrlInSvgUseEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultBrowserSettingEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultClipboardSetting</string>
			<key>pfm_range_list</key>
			<array>
				<integer>2</integer>
				<integer>3</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultCookiesSetting</string>
			<key>pfm_range_list</key>
			<array>
				<integer>1</integer>
				<integer>2</integer>
				<integer>4</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultDownloadDirectory</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultFileSystemReadGuardSetting</string>
			<key>pfm_range_list</key>
			<array>
				<integer>2</integer>
				<integer>3</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultFileSystemWriteGuardSetting</string>
			<key>pfm_range_list</key>
			<array>
				<integer>2</integer>
				<integer>3</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultGeolocationSetting</string>
			<key>pfm_range_list</key>
			<array>
				<integer>1</integer>
				<integer>2</integer>
				<integer>3</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultImagesSetting</string>
			<key>pfm_range_list</key>
			<array>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultInsecureContentSetting</string>
			<key>pfm_range_list</key>
			<array>
				<integer>2</integer>
				<integer>3</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultJavaScriptJitSetting</string>
			<key>pfm_range_list</key>
			<array>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultJavaScriptSetting</string>
			<key>pfm_range_list</key>
			<array>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultLocalFontsSetting</string>
			<key>pfm_range_list</key>
			<array>
				<integer>2</integer>
				<integer>3</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultNotificationsSetting</string>
			<key>pfm_range_list</key>
			<array>
				<integer>1</integer>
				<integer>2</integer>
				<integer>3</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultPopupsSetting</string>
			<key>pfm_range_list</key>
			<array>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultPrinterSelection</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultSearchProviderAlternateURLs</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultSearchProviderContextMenuAccessAllowed</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultSearchProviderEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultSearchProviderEncodings</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultSearchProviderImageURL</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultSearchProviderImageURLPostParams</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultSearchProviderKeyword</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultSearchProviderName</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultSearchProviderNewTabURL</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultSearchProviderSearchURL</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultSearchProviderSearchURLPostParams</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultSearchProviderSuggestURL</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultSearchProviderSuggestURLPostParams</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultSensorsSetting</string>
			<key>pfm_range_list</key>
			<array>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultSerialGuardSetting</string>
			<key>pfm_range_list</key>
			<array>
				<integer>2</integer>
				<integer>3</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultThirdPartyStoragePartitioningSetting</string>
			<key>pfm_range_list</key>
			<array>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultWebBluetoothGuardSetting</string>
			<key>pfm_range_list</key>
			<array>
				<integer>2</integer>
				<integer>3</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultWebHidGuardSetting</string>
			<key>pfm_range_list</key>
			<array>
				<integer>2</integer>
				<integer>3</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultWebUsbGuardSetting</string>
			<key>pfm_range_list</key>
			<array>
				<integer>2</integer>
				<integer>3</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DefaultWindowManagementSetting</string>
			<key>pfm_range_list</key>
			<array>
				<integer>2</integer>
				<integer>3</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DesktopSharingHubEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DevToolsGenAiSettings</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DeveloperToolsAvailability</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>Disable3DAPIs</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DisableAuthNegotiateCnameLookup</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DisablePrintPreview</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DisableSafeBrowsingProceedAnyway</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DisableScreenshots</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DiskCacheDir</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DiskCacheSize</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DnsOverHttpsMode</string>
			<key>pfm_range_list</key>
			<array>
				<string>off</string>
				<string>automatic</string>
				<string>secure</string>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DnsOverHttpsTemplates</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DomainReliabilityAllowed</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DownloadDirectory</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>DownloadRestrictions</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
				<integer>3</integer>
				<integer>4</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>EditBookmarksEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>EnableAuthNegotiatePort</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>EnableExperimentalPolicies</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>EnableMediaRouter</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>EnableOnlineRevocationChecks</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>EncryptedClientHelloEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>EnforceLocalAnchorConstraintsEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>EnterpriseHardwarePlatformAPIEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>EnterpriseProfileCreationKeepBrowsingData</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ExemptDomainFileTypePairsFromFileTypeDownloadWarnings</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>dictionary</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ExplicitlyAllowedNetworkPorts</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ExtensionAllowedTypes</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ExtensionExtendedBackgroundLifetimeForPortConnectionsToUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ExtensionInstallAllowlist</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ExtensionInstallBlocklist</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ExtensionInstallForcelist</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ExtensionInstallSources</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ExtensionInstallTypeBlocklist</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ExtensionManifestV2Availability</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
				<integer>3</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ExtensionSettings</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>dictionary</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ExtensionUnpublishedAvailability</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ExternalProtocolDialogShowAlwaysOpenCheckbox</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>FeedbackSurveysEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>FetchKeepaliveDurationSecondsOnShutdown</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>FileOrDirectoryPickerWithoutGestureAllowedForOrigins</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>FileSystemReadAskForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>FileSystemReadBlockedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>FileSystemWriteAskForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>FileSystemWriteBlockedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>FirstPartySetsEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>FirstPartySetsOverrides</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>dictionary</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ForceEphemeralProfiles</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ForceGoogleSafeSearch</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ForcePermissionPolicyUnloadDefaultEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ForceYouTubeRestrict</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ForcedLanguages</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>GenAILocalFoundationalModelSettings</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>GloballyScopeHTTPAuthCacheEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>GoogleSearchSidePanelEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>HSTSPolicyBypassList</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>HardwareAccelerationModeEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>HeadlessMode</string>
			<key>pfm_range_list</key>
			<array>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>HelpMeWriteSettings</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>HideWebStoreIcon</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>HighEfficiencyModeEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>HistoryClustersVisible</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>HomepageIsNewTabPage</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>HomepageLocation</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>HttpAllowlist</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>HttpsOnlyMode</string>
			<key>pfm_range_list</key>
			<array>
				<string>allowed</string>
				<string>disallowed</string>
				<string>force_enabled</string>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>HttpsUpgradesEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>IPv6ReachabilityOverrideEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>IdleTimeout</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>IdleTimeoutActions</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ImagesAllowedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ImagesBlockedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ImportAutofillFormData</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ImportBookmarks</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ImportHistory</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ImportHomepage</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ImportSavedPasswords</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ImportSearchEngine</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>IncognitoModeAvailability</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>InsecureContentAllowedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>InsecureContentBlockedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>InsecurePrivateNetworkRequestsAllowed</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>InsecurePrivateNetworkRequestsAllowedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>IntensiveWakeUpThrottlingEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>IntranetRedirectBehavior</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
				<integer>3</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>IsolateOrigins</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>JavaScriptAllowedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>JavaScriptBlockedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>JavaScriptJitAllowedForSites</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>JavaScriptJitBlockedForSites</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>LegacySameSiteCookieBehaviorEnabledForDomainList</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>LensDesktopNTPSearchEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>LensRegionSearchEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>LocalFontsAllowedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>LocalFontsBlockedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>LookalikeWarningAllowlistDomains</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ManagedAccountsSigninRestriction</string>
			<key>pfm_range_list</key>
			<array>
				<string>primary_account</string>
				<string>primary_account_strict</string>
				<string>none</string>
				<string>primary_account_keep_existing_data</string>
				<string>primary_account_strict_keep_existing_data</string>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ManagedBookmarks</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>dictionary</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ManagedConfigurationPerOrigin</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>dictionary</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>MaxConnectionsPerProxy</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>MaxInvalidationFetchDelay</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>MediaRecommendationsEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>MediaRouterCastAllowAllIPs</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>MetricsReportingEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>MutationEventsEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>NTPCardsVisible</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>NTPCustomBackgroundEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>NTPMiddleSlotAnnouncementVisible</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>NativeMessagingAllowlist</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>NativeMessagingBlocklist</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>NativeMessagingUserLevelHosts</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>NetworkPredictionOptions</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>NewTabPageLocation</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>NotificationsAllowedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>NotificationsBlockedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>NtlmV2Enabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>OopPrintDriversAllowed</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>OriginAgentClusterDefaultEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>OverrideSecurityRestrictionsOnInsecureOrigin</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PasswordDismissCompromisedAlertEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PasswordLeakDetectionEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PasswordManagerEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PasswordProtectionChangePasswordURL</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PasswordProtectionLoginURLs</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PasswordProtectionWarningTrigger</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PasswordSharingEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PaymentMethodQueryEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PdfLocalFileAccessAllowedForDomains</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PdfUseSkiaRendererEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PdfViewerOutOfProcessIframeEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PolicyAtomicGroupsEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PolicyDictionaryMultipleSourceMergeList</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PolicyListMultipleSourceMergeList</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PolicyRefreshRate</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PopupsAllowedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PopupsBlockedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PostQuantumKeyAgreementEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PrefixedVideoFullscreenApiAvailability</string>
			<key>pfm_range_list</key>
			<array>
				<string>runtime-enabled</string>
				<string>disabled</string>
				<string>enabled</string>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PrintHeaderFooter</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PrintPdfAsImageAvailability</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PrintPdfAsImageDefault</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PrintPreviewUseSystemDefaultPrinter</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PrintRasterizePdfDpi</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PrinterTypeDenyList</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PrintingAllowedBackgroundGraphicsModes</string>
			<key>pfm_range_list</key>
			<array>
				<string>any</string>
				<string>enabled</string>
				<string>disabled</string>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PrintingBackgroundGraphicsDefault</string>
			<key>pfm_range_list</key>
			<array>
				<string>enabled</string>
				<string>disabled</string>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PrintingEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PrintingPaperSizeDefault</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>dictionary</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PrivacySandboxAdMeasurementEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PrivacySandboxAdTopicsEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PrivacySandboxPromptEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PrivacySandboxSiteEnabledAdsEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PrivateNetworkAccessRestrictionsEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ProfilePickerOnStartupAvailability</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ProfileReauthPrompt</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ProfileSeparationDomainExceptionList</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PromotionalTabsEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PromptForDownloadLocation</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>PromptOnMultipleMatchingCertificates</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ProxySettings</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>dictionary</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>QuicAllowed</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RegisteredProtocolHandlers</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>dictionary</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RelatedWebsiteSetsEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RelatedWebsiteSetsOverrides</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>dictionary</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RelaunchNotification</string>
			<key>pfm_range_list</key>
			<array>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RelaunchNotificationPeriod</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RelaunchWindow</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>dictionary</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RemoteAccessHostAllowClientPairing</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RemoteAccessHostAllowFileTransfer</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RemoteAccessHostAllowPinAuthentication</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RemoteAccessHostAllowRelayedConnection</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RemoteAccessHostAllowRemoteAccessConnections</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RemoteAccessHostAllowRemoteSupportConnections</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RemoteAccessHostAllowUrlForwarding</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RemoteAccessHostClientDomainList</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RemoteAccessHostClipboardSizeBytes</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RemoteAccessHostDomainList</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RemoteAccessHostFirewallTraversal</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RemoteAccessHostMatchUsername</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RemoteAccessHostMaximumSessionDurationMinutes</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RemoteAccessHostRequireCurtain</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RemoteAccessHostUdpPortRange</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RemoteDebuggingAllowed</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RequireOnlineRevocationChecksForLocalAnchors</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RestoreOnStartup</string>
			<key>pfm_range_list</key>
			<array>
				<integer>5</integer>
				<integer>1</integer>
				<integer>4</integer>
				<integer>6</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RestoreOnStartupURLs</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RestrictSigninToPattern</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RoamingProfileLocation</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>RoamingProfileSupportEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SSLErrorOverrideAllowed</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SSLErrorOverrideAllowedForOrigins</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SafeBrowsingAllowlistDomains</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SafeBrowsingDeepScanningEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SafeBrowsingExtendedReportingEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SafeBrowsingProtectionLevel</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SafeBrowsingProxiedRealTimeChecksAllowed</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SafeBrowsingSurveysEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SafeSitesFilterBehavior</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SameOriginTabCaptureAllowedByOrigins</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SandboxExternalProtocolBlocked</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SavingBrowserHistoryDisabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ScreenCaptureAllowed</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ScreenCaptureAllowedByOrigins</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ScreenCaptureWithoutGestureAllowedForOrigins</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ScrollToTextFragmentEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SearchSuggestEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SecurityKeyPermitAttestation</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SensorsAllowedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SensorsBlockedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SerialAllowAllPortsForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SerialAllowUsbDevicesForUrls</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>dictionary</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SerialAskForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SerialBlockedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SharedArrayBufferUnrestrictedAccessAllowed</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SharedClipboardEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ShoppingListEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ShowAppsShortcutInBookmarkBar</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ShowCastIconInToolbar</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ShowCastSessionsStartedByOtherDevices</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ShowFullUrlsInAddressBar</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ShowHomeButton</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SideSearchEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SignedHTTPExchangeEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SigninInterceptionEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SitePerProcess</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SpellCheckServiceEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SpellcheckEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>StrictMimetypeCheckForWorkerScriptsEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SuppressDifferentOriginSubframeDialogs</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SuppressUnsupportedOSWarning</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SyncDisabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>SyncTypesListDisabled</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>TabCaptureAllowedByOrigins</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>TabDiscardingExceptions</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>TabOrganizerSettings</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>TaskManagerEndProcessEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ThirdPartyStoragePartitioningBlockedForOrigins</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ToolbarAvatarLabelSettings</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>TotalMemoryLimitMb</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>TranslateEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>URLAllowlist</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>URLBlocklist</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>UrlKeyedAnonymizedDataCollectionEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>UserAgentReduction</string>
			<key>pfm_range_list</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>UserDataDir</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>UserDataSnapshotRetentionLimit</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>UserFeedbackAllowed</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>VideoCaptureAllowed</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>VideoCaptureAllowedUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>WPADQuickCheckEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>WarnBeforeQuittingEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user</string>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>WebAppInstallForceList</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>dictionary</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>WebAppSettings</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>dictionary</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>WebHidAllowAllDevicesForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>WebHidAllowDevicesForUrls</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>dictionary</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>WebHidAllowDevicesWithHidUsagesForUrls</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>dictionary</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>WebHidAskForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>WebHidBlockedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>WebRtcEventLogCollectionAllowed</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>WebRtcIPHandling</string>
			<key>pfm_range_list</key>
			<array>
				<string>default</string>
				<string>default_public_and_private_interfaces</string>
				<string>default_public_interface_only</string>
				<string>disable_non_proxied_udp</string>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>WebRtcLocalIpsAllowedUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>WebRtcTextLogCollectionAllowed</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>WebRtcUdpPortRange</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>WebUsbAllowDevicesForUrls</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>dictionary</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>WebUsbAskForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>WebUsbBlockedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>WindowCaptureAllowedByOrigins</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>WindowManagementAllowedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>WindowManagementBlockedForUrls</string>
			<key>pfm_subkeys</key>
			<array>
				<dict>
					<key>pfm_type</key>
					<string>string</string>
				</dict>
			</array>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>array</string>
		</dict>
		<dict>
			<key>pfm_description</key>
			<string></string>
			<key>pfm_name</key>
			<string>ZstdContentEncodingEnabled</string>
			<key>pfm_targets</key>
			<array>
				<string>user-managed</string>
			</array>
			<key>pfm_title</key>
			<string></string>
			<key>pfm_type</key>
			<string>boolean</string>
		</dict>
	</array>
	<key>pfm_title</key>
	<string></string>
	<key>pfm_version</key>
	<real>1.0</real>
</dict>
</plist>
