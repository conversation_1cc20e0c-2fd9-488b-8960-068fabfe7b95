"Chromium Shortcut" = "Chromium Shortcut";
CFBundleGetInfoString = "Chromium 126.0.6478.0, Autortiesības 2024 Chromium autori. Visas tiesības paturētas.";
NSBluetoothAlwaysUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piek<PERSON>, vietnes varēs lūgt jums piekļuvi.";
NSBluetoothPeripheralUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piekļuve, vietnes varēs lūgt jums piekļuvi.";
NSCameraUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piekļ<PERSON>, vietnes varēs lūgt jums piekļuvi.";
NSHumanReadableCopyright = "Autortiesības 2024 Chromium autori. Visas tiesības paturētas.";
NSLocationUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piek<PERSON>, vietnes varēs lūgt jums piekļuvi.";
NSMicrophoneUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piekļ<PERSON>, vietnes varēs lūgt jums piekļuvi.";
NSWebBrowserPublicKeyCredentialUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piekļuve, vietnes varēs lūgt jums piekļuvi.";
