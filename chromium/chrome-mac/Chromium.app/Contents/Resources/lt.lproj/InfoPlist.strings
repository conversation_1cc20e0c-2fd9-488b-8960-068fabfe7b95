"Chromium Shortcut" = "„Chromium“ spartusis klaviša<PERSON>";
CFBundleGetInfoString = "Chromium 126.0.6478.0, Autorių teisės priklauso „Chromium“ autoriams, 2024 m. Visos teisės saugomos.";
NSBluetoothAlwaysUsageDescription = "Kai „Chromium“ galės pasiekti duomenis, svetainės taip pat galės prašyti suteikti leidimą juos pasiekti.";
NSBluetoothPeripheralUsageDescription = "Kai „Chromium“ galės pasiekti duomenis, svetainės taip pat galės prašyti suteikti leidimą juos pasiekti.";
NSCameraUsageDescription = "Kai „Chromium“ galės pasiekti duomenis, svetainės taip pat galės prašyti suteikti leidimą juos pasiekti.";
NSHumanReadableCopyright = "Autorių teisės priklauso „Chromium“ autoriams, 2024 m. Visos teis<PERSON>s saugomos.";
NSLocationUsageDescription = "<PERSON> „Chromium“ galės pasiekti duomenis, svetain<PERSON><PERSON> taip pat galės prašyti suteikti leidimą juos pasiekti.";
NSMicrophoneUsageDescription = "Kai „Chromium“ galės pasiekti duomenis, svetainės taip pat galės prašyti suteikti leidimą juos pasiekti.";
NSWebBrowserPublicKeyCredentialUsageDescription = "Kai „Chromium“ galės pasiekti duomenis, svetainės taip pat galės prašyti suteikti leidimą juos pasiekti.";
