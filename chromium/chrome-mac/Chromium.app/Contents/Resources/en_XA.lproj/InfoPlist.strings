"Chromium Shortcut" = "Çĥrömîûm Šĥörţçûţ - one two";
CFBundleGetInfoString = "Chromium 126.0.6478.0, Çöþýrîĝĥţ 2024 Ţĥé Çĥrömîûm Åûţĥörš. Åļļ rîĝĥţš réšérvéð. - one two three four five six seven eight";
NSBluetoothAlwaysUsageDescription = "Öñçé Çĥrömîûm ĥåš åççéšš, ŵébšîţéš ŵîļļ bé åbļé ţö åšķ ýöû ƒör åççéšš. - one two three four five six seven eight nine ten - one two three";
NSBluetoothPeripheralUsageDescription = "Öñçé Çĥrömîûm ĥåš åççéšš, ŵébšîţéš ŵîļļ bé åbļé ţö åšķ ýöû ƒör åççéšš. - one two three four five six seven eight nine ten - one two three";
NSCameraUsageDescription = "Öñçé Çĥrömîûm ĥåš åççéšš, ŵébšîţéš ŵîļļ bé åbļé ţö åšķ ýöû ƒör åççéšš. - one two three four five six seven eight nine ten - one two three";
NSHumanReadableCopyright = "Çöþýrîĝĥţ 2024 Ţĥé Çĥrömîûm Åûţĥörš. Åļļ rîĝĥţš réšérvéð. - one two three four five six seven eight";
NSLocationUsageDescription = "Öñçé Çĥrömîûm ĥåš åççéšš, ŵébšîţéš ŵîļļ bé åbļé ţö åšķ ýöû ƒör åççéšš. - one two three four five six seven eight nine ten - one two three";
NSMicrophoneUsageDescription = "Öñçé Çĥrömîûm ĥåš åççéšš, ŵébšîţéš ŵîļļ bé åbļé ţö åšķ ýöû ƒör åççéšš. - one two three four five six seven eight nine ten - one two three";
NSWebBrowserPublicKeyCredentialUsageDescription = "Öñçé Çĥrömîûm ĥåš åççéšš, ŵébšîţéš ŵîļļ bé åbļé ţö åšķ ýöû ƒör åççéšš. - one two three four five six seven eight nine ten - one two three";
