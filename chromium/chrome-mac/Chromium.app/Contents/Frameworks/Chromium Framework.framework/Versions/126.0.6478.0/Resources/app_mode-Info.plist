<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleExecutable</key>
	<string>app_mode_loader</string>
	<key>CFBundleIconFile</key>
	<string>app.icns</string>
	<key>CFBundleIdentifier</key>
	<string>org.chromium.Chromium.app.@APP_MODE_SHORTCUT_ID@</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>@APP_MODE_SHORTCUT_ID@</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>126.0.6478.0</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>6478.0</string>
	<key>CrAppModeShortcutID</key>
	<string>@APP_MODE_SHORTCUT_ID@</string>
	<key>CrAppModeShortcutName</key>
	<string>@APP_MODE_SHORTCUT_NAME@</string>
	<key>CrAppModeShortcutURL</key>
	<string>@APP_MODE_SHORTCUT_URL@</string>
	<key>CrBundleIdentifier</key>
	<string>@APP_MODE_BROWSER_BUNDLE_ID@</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTSDKBuild</key>
	<string>23A334</string>
	<key>DTSDKName</key>
	<string>macosx14.0</string>
	<key>DTXcode</key>
	<string>1500</string>
	<key>DTXcodeBuild</key>
	<string>15A240d</string>
	<key>LSEnvironment</key>
	<dict>
		<key>MallocNanoZone</key>
		<string>0</string>
	</dict>
	<key>LSMinimumSystemVersion</key>
	<string>10.15</string>
	<key>NSAppleScriptEnabled</key>
	<true/>
</dict>
</plist>
