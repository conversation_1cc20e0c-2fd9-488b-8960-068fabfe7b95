<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ASWebAuthenticationSessionWebBrowserSupportCapabilities</key>
	<dict>
		<key>EphemeralBrowserSessionIsSupported</key>
		<true/>
		<key>IsSupported</key>
		<true/>
	</dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Chromium</string>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeIconFile</key>
			<string>document.icns</string>
			<key>CFBundleTypeName</key>
			<string>GIF image</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.compuserve.gif</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeIconFile</key>
			<string>document.icns</string>
			<key>CFBundleTypeName</key>
			<string>HTML document</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.html</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeIconFile</key>
			<string>document.icns</string>
			<key>CFBundleTypeName</key>
			<string>XHTML document</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.xhtml</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeIconFile</key>
			<string>document.icns</string>
			<key>CFBundleTypeName</key>
			<string>JavaScript script</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.netscape.javascript-source</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeIconFile</key>
			<string>document.icns</string>
			<key>CFBundleTypeName</key>
			<string>JPEG image</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.jpeg</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeIconFile</key>
			<string>document.icns</string>
			<key>CFBundleTypeName</key>
			<string>MHTML document</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>org.ietf.mhtml</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeIconFile</key>
			<string>document.icns</string>
			<key>CFBundleTypeName</key>
			<string>HTML5 Audio (Ogg)</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>org.xiph.ogg-audio</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeIconFile</key>
			<string>document.icns</string>
			<key>CFBundleTypeName</key>
			<string>HTML5 Video (Ogg)</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>org.xiph.ogv</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeIconFile</key>
			<string>document.icns</string>
			<key>CFBundleTypeName</key>
			<string>PNG image</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.png</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeIconFile</key>
			<string>document.icns</string>
			<key>CFBundleTypeName</key>
			<string>SVG document</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.svg-image</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeIconFile</key>
			<string>document.icns</string>
			<key>CFBundleTypeName</key>
			<string>Plain text document</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.text</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeIconFile</key>
			<string>document.icns</string>
			<key>CFBundleTypeName</key>
			<string>HTML5 Video (WebM)</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>org.webmproject.webm</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeIconFile</key>
			<string>document.icns</string>
			<key>CFBundleTypeName</key>
			<string>WebP image</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>org.webmproject.webp</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>org.chromium.extension</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeIconFile</key>
			<string>document.icns</string>
			<key>CFBundleTypeName</key>
			<string>PDF Document</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.adobe.pdf</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeIconFile</key>
			<string>document.icns</string>
			<key>CFBundleTypeName</key>
			<string>Chromium Shortcut</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>org.chromium.shortcut</string>
			</array>
		</dict>
	</array>
	<key>CFBundleExecutable</key>
	<string>Chromium</string>
	<key>CFBundleIconFile</key>
	<string>app.icns</string>
	<key>CFBundleIdentifier</key>
	<string>org.chromium.Chromium</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Chromium</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>126.0.6478.0</string>
	<key>CFBundleSignature</key>
	<string>Cr24</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>Web site URL</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>http</string>
				<string>https</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string>Local file URL</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>file</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>6478.0</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTSDKBuild</key>
	<string>23A334</string>
	<key>DTSDKName</key>
	<string>macosx14.0</string>
	<key>DTXcode</key>
	<string>1500</string>
	<key>DTXcodeBuild</key>
	<string>15A240d</string>
	<key>GPUEjectPolicy</key>
	<string>wait</string>
	<key>LSEnvironment</key>
	<dict>
		<key>MallocNanoZone</key>
		<string>0</string>
	</dict>
	<key>LSFileQuarantineEnabled</key>
	<true/>
	<key>LSHasLocalizedDisplayName</key>
	<string>1</string>
	<key>LSMinimumSystemVersion</key>
	<string>10.15</string>
	<key>LSRequiresNativeExecution</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSAppleScriptEnabled</key>
	<true/>
	<key>NSCameraReactionEffectGesturesEnabledDefault</key>
	<false/>
	<key>NSPrincipalClass</key>
	<string>BrowserCrApplication</string>
	<key>NSSupportsAutomaticGraphicsSwitching</key>
	<true/>
	<key>NSUserActivityTypes</key>
	<array>
		<string>NSUserActivityTypeBrowsingWeb</string>
	</array>
	<key>NSUserNotificationAlertStyle</key>
	<string>banner</string>
	<key>OSAScriptingDefinition</key>
	<string>scripting.sdef</string>
	<key>SCMRevision</key>
	<string>dcd3e3fbca881b06fec41cf1a59588f1779daf75-refs/heads/main@{#1300314}</string>
	<key>UTExportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.data</string>
				<string>public.content</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Chromium Extension</string>
			<key>UTTypeIdentifier</key>
			<string>org.chromium.extension</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>crx</string>
				</array>
				<key>public.mime-type</key>
				<array>
					<string>application/x-chrome-extension</string>
				</array>
			</dict>
		</dict>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>com.apple.web-internet-location</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Chromium Shortcut</string>
			<key>UTTypeIdentifier</key>
			<string>org.chromium.shortcut</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>crwebloc</string>
				</array>
				<key>public.mime-type</key>
				<array>
					<string>application/x-chrome-shortcut</string>
				</array>
			</dict>
		</dict>
	</array>
	<key>UTImportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.data</string>
				<string>public.content</string>
			</array>
			<key>UTTypeDescription</key>
			<string>MIME HTML document</string>
			<key>UTTypeIconFile</key>
			<string>document.icns</string>
			<key>UTTypeIdentifier</key>
			<string>org.ietf.mhtml</string>
			<key>UTTypeReferenceURL</key>
			<string>https://www.ietf.org/rfc/rfc2557</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>com.apple.ostype</key>
				<string>MHTM</string>
				<key>public.filename-extension</key>
				<array>
					<string>mht</string>
					<string>mhtml</string>
				</array>
				<key>public.mime-type</key>
				<array>
					<string>multipart/related</string>
					<string>application/x-mimearchive</string>
				</array>
			</dict>
		</dict>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.audio</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Ogg Audio</string>
			<key>UTTypeIconFile</key>
			<string>document.icns</string>
			<key>UTTypeIdentifier</key>
			<string>org.xiph.ogg-audio</string>
			<key>UTTypeReferenceURL</key>
			<string>https://xiph.org/ogg/</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>ogg</string>
					<string>oga</string>
				</array>
				<key>public.mime-type</key>
				<array>
					<string>audio/ogg</string>
				</array>
			</dict>
		</dict>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.movie</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Ogg Video</string>
			<key>UTTypeIconFile</key>
			<string>document.icns</string>
			<key>UTTypeIdentifier</key>
			<string>org.xiph.ogv</string>
			<key>UTTypeReferenceURL</key>
			<string>https://xiph.org/ogg/</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>ogm</string>
					<string>ogv</string>
				</array>
				<key>public.mime-type</key>
				<array>
					<string>video/ogg</string>
				</array>
			</dict>
		</dict>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.movie</string>
			</array>
			<key>UTTypeDescription</key>
			<string>WebM media</string>
			<key>UTTypeIconFile</key>
			<string>document.icns</string>
			<key>UTTypeIdentifier</key>
			<string>org.webmproject.webm</string>
			<key>UTTypeReferenceURL</key>
			<string>https://www.webmproject.org/docs/container/</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>webm</string>
				</array>
				<key>public.mime-type</key>
				<array>
					<string>video/webm</string>
					<string>audio/webm</string>
				</array>
			</dict>
		</dict>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.image</string>
			</array>
			<key>UTTypeDescription</key>
			<string>WebP image</string>
			<key>UTTypeIconFile</key>
			<string>document.icns</string>
			<key>UTTypeIdentifier</key>
			<string>org.webmproject.webp</string>
			<key>UTTypeReferenceURL</key>
			<string>https://developers.google.com/speed/webp/docs/riff_container</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>webp</string>
				</array>
				<key>public.mime-type</key>
				<array>
					<string>image/webp</string>
				</array>
			</dict>
		</dict>
	</array>
</dict>
</plist>
