PLATFORMS := $(or $(PLA<PERSON>ORMS),$(shell echo $$PLATFORMS),linux/amd64)

docker-base-build:
	docker build \
	-t nue-magiclink-to-pdf-base-app:126.0 \
	-f docker/ci.base.Dockerfile .

# docker buildx build --platform $(PLATFORMS) $(BUILD_ARGS) --build-arg VERSION=$(BASE_VERSION) --build-arg RELEASE=$(BASE_RELEASE) --build-arg AUTHORS=$(AUTHORS) -t $(NAME)/base:$(TAG_VERSION) .
docker-app-build:
	docker build \
	-t nue-magiclink-to-pdf-app \
	--build-arg PORT="5300" \
	--build-arg GITLAB_PAT="$GITLAB_PAT" \
	-f ci/ci.app.Dockerfile .

docker-app-local-build:
	docker build \
	-t nue-pdf-app \
    --build-arg PORT="8080" \
    --build-arg VERSION=0.0.1-SNAPSHOT \
	-f docker/App.Dockerfile .
