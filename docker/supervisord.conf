; Documentation of this file format -> http://supervisord.org/configuration.html

[supervisord]
childlogdir=/var/log/supervisor               ; ('AUTO' child log dir, default $TEMP)
logfile=/var/log/supervisor/supervisord.log   ; (main log file;default $CWD/supervisord.log)
logfile_maxbytes=50MB                         ; (max main logfile bytes b4 rotation;default 50MB)
logfile_backups=10                            ; (num of main logfile rotation backups;default 10)
loglevel=%(ENV_SE_SUPERVISORD_LOG_LEVEL)s     ; (log level;default info; others: debug,warn,trace) http://supervisord.org/logging.html
pidfile=/var/run/supervisor/supervisord.pid   ; (supervisord pidfile;default supervisord.pid)
nodaemon=true                                 ; (start in foreground if true;default false)
minfds=1024                                   ; (min. avail startup file descriptors;default 1024)
minprocs=200                                  ; (min. avail process descriptors;default 200)

[unix_http_server]
file=/tmp/supervisor.sock                     ; (the path to the socket file)
chmod=0700
username=%(ENV_SEL_USER)s
password=secret

; the below section must remain in the config file for RPC
; (supervisorctl/web interface) to work, additional interfaces may be
; added by defining them in separate rpcinterface: sections
[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///tmp/supervisor.sock         ; use a unix:// URL  for a unix socket

[include]
files = /etc/supervisor/conf.d/*.conf
