#!/bin/bash

WRAPPER_PATH=$(readlink -f /usr/bin/chromium)
BASE_PATH="$WRAPPER_PATH-base"
mv "$WRAPPER_PATH" "$BASE_PATH"

cat > "$WRAPPER_PATH" <<_EOF
#!/bin/bash

# umask 002 ensures default permissions of files are 664 (rw-rw-r--) and directories are 775 (rwxrwxr-x).
umask 002

# Debian/Ubuntu seems to not respect --lang, it instead needs to be a LANGUAGE environment var
# See: https://stackoverflow.com/a/41893197/359999
for var in "\$@"; do
   if [[ \$var == --lang=* ]]; then
      LANGUAGE=\${var//--lang=}
   fi
done

# Set language environment variable
export LANGUAGE="\$LANGUAGE"

# Capture the filtered environment variables start with "SE_BROWSER_ARGS_" into an array
mapfile -t BROWSER_ARGS_ARRAY < <(printenv | grep ^SE_BROWSER_ARGS_)
# Iterate over the array
for var in "\${BROWSER_ARGS_ARRAY[@]}"; do
  # Split the variable into name and value
  IFS='=' read -r name value <<< "\$var"
  SE_BROWSER_ARGS="\$SE_BROWSER_ARGS \$value"
done

# Note: exec -a below is a bashism.
exec -a "\$0" "$BASE_PATH" --no-sandbox \$SE_BROWSER_ARGS "\$@"
_EOF
chmod +x "$WRAPPER_PATH"
