FROM selenium/standalone-chromium:127.0.6533.99

ARG PORT
ARG VERSION
ENV VERSION=${VERSION}

RUN wget -q -O - https://download.bell-sw.com/pki/GPG-KEY-bellsoft | sudo apt-key add -
RUN echo "deb https://apt.bell-sw.com/ stable main" | sudo tee /etc/apt/sources.list.d/bellsoft.list
RUN sudo apt-get update
RUN sudo apt-get install bellsoft-java21-runtime

WORKDIR /app

COPY pdf-application/target/pdf-application.jar ./pdf-application-${VERSION}.jar
COPY --chmod=755 start.sh /app/start.sh

EXPOSE ${PORT}

CMD ["/app/start.sh"]