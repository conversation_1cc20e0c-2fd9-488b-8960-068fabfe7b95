; Documentation of this file format -> http://supervisord.org/configuration.html

; Priority 0 - xvfb & fluxbox, 5 - x11vnc, 10 - noVNC, 15 - selenium-node

[program:browserleftoverscleanup]
priority=20
command=bash -c "if [ ${SE_ENABLE_BROWSER_LEFTOVERS_CLEANUP} = "true" ]; then /opt/bin/chrome-cleanup.sh; fi"
autostart=%(ENV_SE_ENABLE_BROWSER_LEFTOVERS_CLEANUP)s
autorestart=%(ENV_SE_ENABLE_BROWSER_LEFTOVERS_CLEANUP)s
stopsignal=INT

;Logs
redirect_stderr=false
stdout_logfile=/var/log/supervisor/browser-leftover-cleanup-stdout.log
stderr_logfile=/var/log/supervisor/browser-leftover-cleanup-stderr.log
stdout_logfile_maxbytes=50MB
stderr_logfile_maxbytes=50MB
stdout_logfile_backups=5
stderr_logfile_backups=5
stdout_capture_maxbytes=50MB
stderr_capture_maxbytes=50MB
