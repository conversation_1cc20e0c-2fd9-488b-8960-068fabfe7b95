FROM ubuntu:noble-20240605
LABEL authors="Nue DEV group"

# Arguments to define the version of dependencies to download
ARG VERSION
ARG RELEASE=selenium-${VERSION}
ARG AUTHORS=SeleniumHQ
# Default value should be aligned with upstream Selenium (https://github.com/SeleniumHQ/selenium/blob/trunk/MODULE.bazel)
ARG OPENTELEMETRY_VERSION=1.40.0
ARG GRPC_VERSION=1.65.1
ARG NETTY_VERSION=4.1.111.Final
ARG CS_VERSION=2.1.10

#Arguments to define the user running Selenium
ARG HOME=/home/<USER>
ARG UID=1200
ARG GID=1201
ARG TZ="UTC"
ARG JRE_VERSION=17
ARG TARGETARCH
ARG TARGETVARIANT

USER root

ENV DEBIAN_FRONTEND=noninteractive \
    # No interactive frontend during docker build
    DEBCONF_NONINTERACTIVE_SEEN=true \
    HOME=${HOME} \
    TZ=${TZ} \
    SEL_DOWNLOAD_DIR=${HOME}/Downloads 

#========================
# Miscellaneous packages
# Includes minimal runtime used for executing non GUI Java programs
#========================
RUN  if [ "$TARGETARCH" = "amd64" ]; then \
        echo "deb http://archive.ubuntu.com/ubuntu noble main universe\n" > /etc/apt/sources.list \
        && echo "deb http://archive.ubuntu.com/ubuntu noble-updates main universe\n" >> /etc/apt/sources.list \
        && echo "deb http://security.ubuntu.com/ubuntu noble-security main universe\n" >> /etc/apt/sources.list ; \
     elif [ "$TARGETARCH" = "arm64" ]; then \
        # This covers both arm64 and arm64/v8
        echo "deb http://ports.ubuntu.com/ubuntu-ports noble main universe restricted multiverse\n" > /etc/apt/sources.list \
        && echo "deb http://ports.ubuntu.com/ubuntu-ports noble-updates main universe restricted multiverse\n" >> /etc/apt/sources.list \
        && echo "deb http://ports.ubuntu.com/ubuntu-ports noble-security main universe restricted multiverse\n" >> /etc/apt/sources.list ; \
     fi \
  && apt-get -qqy update \
  && apt-get upgrade -yq \
  && apt-get -qqy --no-install-recommends install \
    acl \
    bzip2 \
    ca-certificates \
    tzdata \
    sudo \
    unzip \
    wget \
    jq \
    curl \
    supervisor \
    gnupg2 \
    openjdk-17-jdk \
  && if [ "${TARGETARCH}" = "arm" ] && [ "${TARGETVARIANT}" = "v7" ]; then \
       export ARCH=armhf ; \
    else \
       export ARCH=$(dpkg --print-architecture) ; \
    fi \
  && sed -i 's/securerandom\.source=file:\/dev\/random/securerandom\.source=file:\/dev\/urandom/' /usr/lib/jvm/java-${JRE_VERSION}-openjdk-${ARCH}/conf/security/java.security \
  && rm -rf /var/lib/apt/lists/* /var/cache/apt/*

#==========
# Selenium & relaxing permissions for OpenShift and other non-sudo environments
#==========
RUN mkdir -p /opt/selenium /opt/selenium/assets /opt/selenium/secrets /var/run/supervisor /var/log/supervisor ${SEL_DOWNLOAD_DIR} \
    ${HOME}/.mozilla ${HOME}/.vnc ${HOME}/.pki/nssdb \
  && touch /opt/selenium/config.toml \
  && chmod -R 775 /opt/selenium /var/run/supervisor /var/log/supervisor /etc/passwd ${HOME} \
  
#=====
# Download observability related OpenTelemetry jars and make them available in a separate directory
# so that the container can skip downloading them everytime it comes up
#===== \
  && if [ `arch` = "aarch64" ] || [ `arch` = "x86_64" ]; then \
        curl -fL https://github.com/coursier/coursier/releases/download/v${CS_VERSION}/coursier.jar > /tmp/cs \
        && chmod +x /tmp/cs \
        && mkdir -p /external_jars \
        && chmod -R 775 /external_jars ; \
     fi \
  && if [ -f "/tmp/cs" ]; then \
        java -jar /tmp/cs fetch --classpath --cache /external_jars \
        io.opentelemetry:opentelemetry-exporter-otlp:${OPENTELEMETRY_VERSION} \
        io.grpc:grpc-netty:${GRPC_VERSION} io.netty:netty-codec-http:${NETTY_VERSION} > /external_jars/.classpath.txt \
        && chmod 664 /external_jars/.classpath.txt ; \
     fi \
  && rm -fr /root/.cache/* \
  # (Note that .bashrc is only executed in interactive bash shells.)
  && echo 'if [[ $(ulimit -n) -gt 200000 ]]; then echo "WARNING: Very high value reported by \"ulimit -n\". Consider passing \"--ulimit nofile=32768\" to \"docker run\"."; fi' >> ${HOME}/.bashrc

#======================================
# Add Grid check script
#======================================
COPY docker/entry_point.sh /opt/bin/

#======================================
# Add Supervisor configuration file
#======================================
COPY docker/supervisord.conf /etc

#======================================
# Configure environement
#======================================
    # Boolean value, maps "--bind-host"
ENV SE_BIND_HOST=false \
    SE_SERVER_PROTOCOL="http" \
    SE_REJECT_UNSUPPORTED_CAPS=false \
    SE_OTEL_JAVA_GLOBAL_AUTOCONFIGURE_ENABLED=true \
    SE_OTEL_TRACES_EXPORTER="otlp" \
    SE_SUPERVISORD_LOG_LEVEL="info" \
    SE_LOG_LEVEL="INFO" \
    SE_HTTP_LOGS=false \
    SE_STRUCTURED_LOGS=false \
    SE_ENABLE_TRACING=true \
    SE_ENABLE_TLS=false \
    SE_JAVA_DISABLE_HOSTNAME_VERIFICATION=true

USER root

# Install Chromium
RUN echo "deb http://deb.debian.org/debian/ sid main" >> /etc/apt/sources.list \
  && apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys 0E98404D386FA1D9 6ED0E7B82643E131 \
  && apt-get update -qqy \
  && apt-get -qqy install chromium=127.0.6533.88 \
  && rm -rf /var/lib/apt/lists/* /var/cache/apt/*

#=================================
# Chromium Launch Script Wrapper
#=================================
COPY docker/wrap_chromium_binary /opt/bin/wrap_chromium_binary
RUN /opt/bin/wrap_chromium_binary

#============================================
# Chromium webdriver
#============================================
RUN apt-get update -qqy \
  && apt-get -qqy install chromium-driver \
  && rm -rf /var/lib/apt/lists/* /var/cache/apt/*

#============================================
# Chromium cleanup script and supervisord file
#============================================
COPY docker/chrome-cleanup.sh /opt/bin/chrome-cleanup.sh
COPY docker/chrome-cleanup.conf /etc/supervisor/conf.d/chrome-cleanup.conf

#============================================
# Dumping Browser information for config
#============================================
RUN echo "chrome" > /opt/selenium/browser_name
RUN chromium --version | awk '{print $2}' > /opt/selenium/browser_version
RUN echo "\"goog:chromeOptions\": {\"binary\": \"/usr/bin/chromium\"}" > /opt/selenium/browser_binary_location

WORKDIR /app
