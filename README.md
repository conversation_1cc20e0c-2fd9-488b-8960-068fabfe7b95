# Nue Pdf Generator

### Feature includes
Must set SPRING_PROFILES_ACTIVE=dev when running this application in dev environment.
### Build
```shell
mvn clean install
```
#### Sample rest apis
#### User Session and Logging filters
For local development, use spring profile as `dev` for logging into console.
For other env, the logs will be logged with JSON format.

#### API doc and client generator
To generate the openapi docs and client.
```shell
mvn -PapiDoc clean install
mvn -PapiDoc clean install -Dspring.profiles.active=dev -DskipTests
```

#### Docker
Build application docker image
```shell
cd application && docker build --build-arg BUILD_DIR=target --build-arg VERSION=0.0.1-SNAPSHOT --build-arg PORT=5300 . -f docker/Dockerfile -t nue-magiclink-to-pdf-app
```
This script is used to init local dev environment.
```shell
/bin/bash ./chromium-dev-init.sh
```
Docker image is built ref to:
https://github.com/SeleniumHQ/docker-selenium/tree/trunk/NodeChromium


