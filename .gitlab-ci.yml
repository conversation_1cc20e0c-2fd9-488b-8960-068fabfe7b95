stages:
    - test
    - setup-db-branch
    - setup-db
    - build
    - docker-build
    - setup
    - upgrade-infra
    - db-migration
    - deploy

include:
    - template: Jobs/Dependency-Scanning.latest.gitlab-ci.yml
      rules:
        - if: $CI_PIPELINE_SOURCE == 'merge_request_event'

    - template: Jobs/Secret-Detection.latest.gitlab-ci.yml
      rules:
        - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
        
    - template: Jobs/SAST.latest.gitlab-ci.yml
      rules:
        - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    

    - project: 'nue-apps/nue-ci'
      ref: 'main'
      file: 'templates/Docker-Build.latest.gitlab-ci.yml'

    - component: gitlab.com/nue-apps/nue-components-unit-test/maven-unit-test@1.0
      inputs:
        image: ${CI_DEPENDENCY_PROXY_DIRECT_GROUP_IMAGE_PREFIX}/maven:3.9.8-eclipse-temurin-21-alpine
        stage: test
        submodule-path: pdf-application
        junit-report-path: pdf-application/target/surefire-reports/*.xml

    - component: gitlab.com/nue-apps/nue-components-terraform/terraform-plan@1.0
      inputs:
        app_name: auth
      rules:
        - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
          changes:
            - terraform/**/*

    - component: gitlab.com/nue-apps/nue-components-microservice-deploy/microservice-deploy@1
      inputs:
        environment: "${ENVIRONMENT}" 
        image_version: "${IMAGE_VERSION}"
      rules:
        - if: $MICROSERVICE_DEPLOY == "true"

variables:
    TF_VAR_gitlab_access_token: "${CI_JOB_TOKEN}"
    **********************: "${CI_REGISTRY_IMAGE}"
    SECURE_LOG_LEVEL: debug


build-app-image:
    extends: .docker-build-job
    stage: docker-build
    variables:
        DOCKERFILE_RELATIVE_PATH: "ci/ci.app.Dockerfile"
        IMAGE_REGISTRY: "${CI_REGISTRY_IMAGE}"
        IMAGE_TAG: "${NUE_VERSION}"
        IMAGE_VERSION: "${IMAGE_VERSION}"
        CACHE_ENABLED: "true"
        APP_NAME: "pdf-application"
        SERVICE_NAME: "${ENVIRONMENT}-pdf"
        PORT: "8080"
        BUILD_ARGS: "--build-arg MVN_USERNAME=${MVN_USERNAME} --build-arg MVN_PASSWORD=${MVN_PASSWORD} --build-arg PORT=${PORT} --build-arg APP_NAME=${APP_NAME} --build-arg OTEL_API_KEY=${OTEL_API_KEY} --build-arg OTEL_ENDPOINT=${OTEL_ENDPOINT}  --build-arg SERVICE_NAME=${SERVICE_NAME} --build-arg JAVA_TOOL_OPTIONS=${JAVA_TOOL_OPTIONS} --build-arg ENVIRONMENT=${ENVIRONMENT} --build-arg GITLAB_PAT=${GITLAB_PAT} --build-arg OPEN_TELEMETRY_LINK=${OPEN_TELEMETRY_LINK}"
    rules:
    - if: $DOCKER_BUILD == "true"
    - if: "$CI_PIPELINE_SOURCE == 'merge_request_event'"
      variables:
        IMAGE_VERSION: unit-tests-$CI_MERGE_REQUEST_IID


aws-ecs-run-db-migration-job:
  stage: db-migration
  script:
    - echo ""
  rules:
    - when: never