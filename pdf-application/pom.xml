<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>nue-pdf</artifactId>
        <groupId>io.nue</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>pdf-application</artifactId>

    <properties>
        <apidoc.server.url>http://localhost:9999</apidoc.server.url>
        <apidoc.app.name>pdf</apidoc.app.name>
        <nue.utils.version>1.7.4-SNAPSHOT</nue.utils.version>
        <selenium.version>4.23.0</selenium.version>
        <webdrivermanager.version>5.9.1</webdrivermanager.version>
        <httpclient5.version>5.2.1</httpclient5.version>
        <pdfbox.version>3.0.2</pdfbox.version>
        <bouncycastle.version>1.70</bouncycastle.version>
        <junit.version>4.13.1</junit.version>
        <slf4j-api.version>1.7.32</slf4j-api.version>
        <jacoco-version>0.8.11</jacoco-version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>6.1.13</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>6.1.13</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-webmvc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
            <version>6.8.1</version>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <version>6.0.0</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.ruby</groupId>
            <artifactId>ruby-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.13.3</version> <!-- Use the latest version available -->
        </dependency>
<!--        <dependency>
            <groupId>io.nue</groupId>
            <artifactId>pdf-api-client</artifactId>
            <version>${project.version}</version>
        </dependency>-->
        <dependency>
            <groupId>io.nue</groupId>
            <artifactId>common-utils</artifactId>
            <version>${nue.utils.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-jdbc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.seleniumhq.selenium</groupId>
            <artifactId>selenium-java</artifactId>
            <version>${selenium.version}</version>
        </dependency>
        <dependency>
            <groupId>io.github.bonigarcia</groupId>
            <artifactId>webdrivermanager</artifactId>
            <version>${webdrivermanager.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
            <version>${httpclient5.version}</version>
        </dependency>
        <dependency>
            <groupId>com.aayushatharva.brotli4j</groupId>
            <artifactId>native-osx-aarch64</artifactId>
            <version>1.16.0</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-resolver-dns-native-macos</artifactId>
            <scope>runtime</scope>
            <classifier>osx-aarch_64</classifier>
            <version>4.1.92.Final</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>${pdfbox.version}</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>${bouncycastle.version}</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
            <version>${bouncycastle.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
            <version>${junit.version}</version>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-models</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.slf4j</groupId>-->
<!--            <artifactId>slf4j-api</artifactId>-->
<!--            <version>${slf4j-api.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <version>1.5.6</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.5.6</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-handler</artifactId>
            <version>4.1.94.Final</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
            <version>1.78</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-codec-http</artifactId>
            <version>4.1.108.Final</version>
        </dependency>
        <dependency>
            <groupId>org.xmlunit</groupId>
            <artifactId>xmlunit-core</artifactId>
            <version>2.10.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
            <version>5.3.39</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>apiDoc</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <configuration>
                            <excludes>
                                <exclude>
                                    <groupId>org.springframework.boot</groupId>
                                    <artifactId>spring-boot-configuration-processor</artifactId>
                                </exclude>
                            </excludes>
                            <profiles>
                                <!--suppress UnresolvedMavenProperty -->
                                <profile>${spring.profiles.active}</profile>
                            </profiles>
                        </configuration>
                        <executions>
                            <execution>
                                <id>pre-integration-test</id>
                                <goals>
                                    <goal>start</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>post-integration-test</id>
                                <goals>
                                    <goal>stop</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.springdoc</groupId>
                        <artifactId>springdoc-openapi-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>integration-test</id>
                                <goals>
                                    <goal>generate</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <apiDocsUrl>${apidoc.server.url}/v3/api-docs</apiDocsUrl>
                            <outputFileName>pdf-api.json</outputFileName>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.openapitools</groupId>
                        <artifactId>openapi-generator-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <phase>install</phase>
                                <goals>
                                    <goal>generate</goal>
                                </goals>
                                <configuration>
                                    <inputSpec>${project.build.directory}/${apidoc.app.name}-api.json</inputSpec>
                                    <generatorName>java</generatorName>
                                    <invokerPackage>${project.groupId}.${apidoc.app.name}.client</invokerPackage>
                                    <apiPackage>${project.groupId}.${apidoc.app.name}.client.api</apiPackage>
                                    <modelPackage>${project.groupId}.${apidoc.app.name}.client.model</modelPackage>
                                    <groupId>${project.groupId}</groupId>
                                    <artifactId>${project.artifactId}-api-client</artifactId>
                                    <artifactVersion>${project.version}</artifactVersion>
                                    <output>${generated.client.dir}</output>
                                    <configOptions>
                                        <java8>true</java8>
                                        <useBeanValidation>true</useBeanValidation>
                                        <serializationLibrary>jackson</serializationLibrary>
                                        <dateLibrary>java8</dateLibrary>
                                    </configOptions>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>io.nue.pdf.PdfApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco-version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>jacoco-report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <!-- <execution><id>jacoco-check</id><goals><goal>check</goal></goals><configuration><rules><rule><element>PACKAGE</element><limits><limit><counter>LINE</counter><value>COVEREDRATIO</value><minimum>0.9</minimum></limit></limits></rule></rules></configuration></execution> -->
                </executions>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springdoc</groupId>
                    <artifactId>springdoc-openapi-maven-plugin</artifactId>
                    <version>1.1</version>
                </plugin>
                <plugin>
                    <groupId>org.openapitools</groupId>
                    <artifactId>openapi-generator-maven-plugin</artifactId>
                    <version>5.1.0</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>