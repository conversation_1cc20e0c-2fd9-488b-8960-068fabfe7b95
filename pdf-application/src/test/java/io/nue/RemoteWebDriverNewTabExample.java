package io.nue;

import org.junit.Ignore;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.remote.RemoteWebDriver;
import java.net.URL;
import java.util.ArrayList;

@Ignore
public class RemoteWebDriverNewTabExample {
    public static void main(String[] args) throws Exception {
        ChromeOptions options = new ChromeOptions();
        URL remoteUrl = new URL("http://localhost:4444/wd/hub"); // Replace with your Selenium Grid URL
        RemoteWebDriver driver = new RemoteWebDriver(remoteUrl, options);

        try {
            // Navigate to a website in the first tab
            driver.get("https://www.example.com");

            // Open a new tab using JavaScript
            ((JavascriptExecutor) driver).executeScript("window.open()");

            // Get all window handles
            ArrayList<String> tabs = new ArrayList<>(driver.getWindowHandles());

            // Switch to the new tab (it will be the last one in the list)
            driver.switchTo().window(tabs.get(tabs.size() - 1));

            // Navigate to a different website in the new tab
            driver.get("https://www.google.com");

            // Print the title of the new tab
            System.out.println("New tab title: " + driver.getTitle());

            // Switch back to the first tab
            driver.switchTo().window(tabs.get(0));

            // Print the title of the first tab
            System.out.println("First tab title: " + driver.getTitle());

        } finally {
            // Close the browser
            driver.quit();
        }
    }
}
