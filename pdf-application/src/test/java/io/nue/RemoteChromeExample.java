package io.nue;
import io.nue.pdf.service.SeleniumServiceImpl;
import org.junit.Ignore;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.time.Duration;
import java.util.Set;

@Ignore
public class RemoteChromeExample {

    private static final Logger log = LoggerFactory.getLogger(RemoteChromeExample.class);
    
    public static void main(String[] args) throws Exception {
        // Set up Chrome options
        ChromeOptions options = new ChromeOptions();
        options.addArguments("--headless");
        options.addArguments("--no-sandbox");
        options.addArguments("--disable-dev-shm-usage");
        options.addArguments("--start-maximized");
        options.addArguments("--disable-infobars");

        // URL of the remote Selenium Grid hub
        URL remoteUrl = new URL("http://localhost:4444/wd/hub");

        // Create a RemoteWebDriver instance
        RemoteWebDriver driver = new RemoteWebDriver(remoteUrl, options);
        driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(30));

        try {
            
            Set<String> allHandlers = driver.getWindowHandles();
            
            int currentHandlerCount = allHandlers.size();
            log.info(" Thread {}, currentHandlerCount :{}", Thread.currentThread().getId(), currentHandlerCount);
            JavascriptExecutor jsExecutor = (JavascriptExecutor) driver;
            jsExecutor.executeScript("window.open('about:blank', '_blank')");
            allHandlers = driver.getWindowHandles();
            log.info(" Thread {}, NEW currentHandlerCount :{}", Thread.currentThread().getId(), allHandlers.size());
            String newHandle = allHandlers.toArray()[currentHandlerCount].toString();
            driver.switchTo().window(newHandle);

            // Navigate to a website
            driver.get("https://www.google.com");

            // Find the search box element
            WebElement searchBox = driver.findElement(By.name("q"));

            // Type into the search box
            searchBox.sendKeys("Selenium WebDriver");

            // Submit the search form
            searchBox.submit();

            // Wait for search results
            WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(20));
            wait.until(ExpectedConditions.presenceOfElementLocated(By.id("search")));

            // Find and click the first search result
            WebElement firstResult = driver.findElement(By.cssSelector("div.g h3"));
            firstResult.click();

            // Wait for the page to load
            wait.until(ExpectedConditions.titleContains("Selenium"));

            // Print the URL of the new page
            System.out.println("New page URL: " + driver.getCurrentUrl());

            // Go back to the search results
//            driver.navigate().back();
//
//            // Verify we're back on the search page
//            wait.until(ExpectedConditions.titleContains("Selenium WebDriver"));

            System.out.println("Back to search results: " + driver.getTitle());

        } finally {
            // Close the browser
            driver.quit();
        }
    }
}
