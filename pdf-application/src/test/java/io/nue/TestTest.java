package io.nue;

import com.google.common.collect.Lists;
import io.nue.util.json.JsonUtils;
import org.junit.Test;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

public class TestTest {

    @Test
    public void test() {
        String stackTrace = "org.openqa.selenium.TimeoutException: Expected condition failed: waiting for io.nue.pdf.bean.WebDriverPair$$Lambda/0x00007f6772a4d798@5af94c76 (tried for 20 second(s) with 300 milliseconds interval)\nBuild info: version: '4.23.0', revision: '4df0a231af'\nSystem info: os.name: 'Linux', os.arch: 'amd64', os.version: '5.10.225-213.878.amzn2.x86_64', java.version: '21.0.4'\nDriver info: org.openqa.selenium.chrome.ChromeDriver\nCapabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 126.0.6478.182, chrome: {chromedriverVersion: 126.0.6478.182 (5b5d8292ddf..., userDataDir: /tmp/.org.chromium.Chromium...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:46009}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: Proxy(), se:cdp: ws://localhost:46009/devtoo..., se:cdpVersion: 126.0.6478.182, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\nSession ID: d25ba6587822fe967ff0eaa2a37a8d2c\n\tat org.openqa.selenium.support.ui.WebDriverWait.timeoutException(WebDriverWait.java:84)\n\tat org.openqa.selenium.support.ui.FluentWait.until(FluentWait.java:228)\n\tat io.nue.pdf.bean.WebDriverPair.invokePdfGeneration(WebDriverPair.java:141)\n\tat io.nue.pdf.bean.WebDriverPair.downloadPdf(WebDriverPair.java:164)\n\tat io.nue.pdf.service.SeleniumServiceImpl.downloadPdfByUrl(SeleniumServiceImpl.java:43)\n\tat io.nue.pdf.service.InvoicePdfServiceImpl.lambda$magicklinkPdf$0(InvoicePdfServiceImpl.java:39)\n\tat java.base/java.util.concurrent.FutureTask.run(Unknown Source)\n\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)\n\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)\n\tat java.base/java.lang.Thread.run(Unknown Source)\n";
        List<String> command = Lists.newArrayList(stackTrace.split("\\."));

        int index = command.indexOf("--print-to-pdf");
        command.add(index + 1, "insert");

        System.out.println(JsonUtils.serialize(command));

        Path path = Paths.get("/Users/<USER>/Downloads", "huhusf.pdf");
        System.out.println(path.toString());
    }   
}
