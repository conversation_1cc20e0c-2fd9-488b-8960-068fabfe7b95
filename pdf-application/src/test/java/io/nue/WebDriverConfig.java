package io.nue;

import io.github.bonigarcia.wdm.WebDriverManager;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.chromium.ChromiumOptions;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.URL;
import java.time.Duration;
import java.util.Objects;

import javax.annotation.PostConstruct;

@Configuration
public class WebDriverConfig {

    private static final Logger log = LoggerFactory.getLogger(WebDriverConfig.class);
    private WebDriver driver;
    private WebDriverWait driverWait;
    @Autowired
    private ChromeOptions chromeOptions;

    @PostConstruct
    public void initialize() {
        // Shutdown hook
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            if (isDriverLoaded()) {
                log.info("Shutdown signal detected: Closing opened drivers");
                quitDriver();
                log.info("Opened drivers closed");
            }
        }));

        // Disable driver log output
        System.setProperty("webdriver.chrome.silentOutput", "true");
    }

    private synchronized boolean isDriverLoaded() {
        return driver != null;
    }

    @Bean("webDriver")
    public WebDriver webDriver() {
        log.info("Creating Driver");
        if (driver == null) {
            initialiseDriver();
        }

        return driver;
    }

    @Bean("webDriverWait")
    public WebDriverWait webDriverWait() {
        log.info("Creating Driver Wait");
        if (driver == null) {
            initialiseDriver();
        }

        return driverWait;
    }

    public void quitDriver() {
        if (driver == null) {
            return;
        }

        driver.quit();
        driver = null;
    }

    private void initialiseDriver() {
        try {
            // Prevent mem leak
            if (!Objects.isNull(driver)) {
                quitDriver();
            }
            log.info("Chrome Driver :{}", chromeOptions);
            driver = new ChromeDriver(chromeOptions);
            WebDriverManager.chromiumdriver().setup();
            log.info("Driver created successfully");
            driverWait = new WebDriverWait(driver, Duration.ofSeconds(30));
            // timeouts
            driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(30));
            driver.manage().timeouts().pageLoadTimeout(Duration.ofSeconds(30));
        } catch (Exception e) {
            log.error("Error while creating driver", e);
        }
    }

    public void setChromeOptions(ChromeOptions chromeOptions) {
        this.chromeOptions = chromeOptions;
    }
}
