package io.nue;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.encryption.AccessPermission;
import org.apache.pdfbox.pdmodel.encryption.StandardProtectionPolicy;
import org.junit.Ignore;

import java.io.IOException;

@Ignore
public class UnmodifiablePDFCreator {
    public static void main(String[] args) {
        try (PDDocument document = new PDDocument()) {
            PDPage page = new PDPage();
            document.addPage(page);

            try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                contentStream.beginText();
                
                contentStream.newLineAtOffset(100, 700);
                contentStream.showText("This is an unmodifiable PDF document.");
                contentStream.endText();
            }

            // Set up the AccessPermission
            AccessPermission ap = new AccessPermission();
            ap.setCanModify(false);
            ap.setCanExtractContent(false);
            ap.setCanPrint(false);
            ap.setCanModifyAnnotations(false);
            ap.setCanFillInForm(false);
            ap.setCanExtractForAccessibility(false);
            ap.setCanAssembleDocument(false);
            

            // Owner password (to open the document with full permissions)
            String ownerPassword = "owner123";
            // User password (to open the document with restricted permissions)
            String userPassword = "user123";

            StandardProtectionPolicy spp = new StandardProtectionPolicy(ownerPassword, userPassword, ap);
            spp.setEncryptionKeyLength(256);

            document.protect(spp);

            document.save("unmodifiable.pdf");
            System.out.println("Unmodifiable PDF created successfully.");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
