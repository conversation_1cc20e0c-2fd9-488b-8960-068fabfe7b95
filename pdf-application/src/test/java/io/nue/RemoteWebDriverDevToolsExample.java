package io.nue;

import org.junit.Ignore;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.devtools.HasDevTools;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.openqa.selenium.devtools.DevTools;
import org.openqa.selenium.devtools.v85.network.Network;

import java.net.URL;
import java.util.Optional;

@Ignore
public class RemoteWebDriverDevToolsExample {
    public static void main(String[] args) throws Exception {
        ChromeOptions options = new ChromeOptions();
        URL remoteUrl = new URL("http://localhost:4444/wd/hub"); // Replace with your Selenium Grid URL
        RemoteWebDriver driver = new RemoteWebDriver(remoteUrl, options);

        try {
            // Get the DevTools object
            DevTools devTools = ((HasDevTools) driver).getDevTools();
            devTools.createSession();

            // Enable network tracking
            devTools.send(Network.enable(Optional.empty(), Optional.empty(), Optional.empty()));

            // Add a listener for Network events
            devTools.addListener(Network.requestWillBeSent(),
                    request -> System.out.println("Request URL: " + request.getRequest().getUrl()));

            // Navigate to a page
            driver.get("https://www.example.com");

            // Wait a bit to see some network requests
            Thread.sleep(5000);

        } finally {
            driver.quit();
        }
    }
}
