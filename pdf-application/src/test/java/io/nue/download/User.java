package io.nue.download;

import java.util.ArrayList;
import java.util.List;

public class User {
    private String id;
    private List<String> fileIds;

    public User(String id) {
        this.id = id;
        this.fileIds = new ArrayList<>();
    }

    public void addFile(String fileId) {
        fileIds.add(fileId);
    }

    public List<String> getFileIds() { return fileIds; }
    public String getId() { return id; }
}
