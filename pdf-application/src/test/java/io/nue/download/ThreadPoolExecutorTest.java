package io.nue.download;

import static java.lang.Thread.sleep;

import io.nue.pdf.config.AppConfiguration;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;

public class ThreadPoolExecutorTest {

    public static void main(String[] args) {
        AppConfiguration appConfig = new AppConfiguration();
        ThreadPoolExecutor executor = appConfig.pdfTaskExecutor(1);

        List<Future<String>> futures = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            final int taskId = i;
            Future<String> future = executor.submit(() -> {
                sleep(10000);
                return "Hello World!" + taskId;
            });
            futures.add(future);
        }
        System.out.println(futures.size());
        for (Future<String> future : futures) {
            try {
                System.out.println(future.get());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }
}
