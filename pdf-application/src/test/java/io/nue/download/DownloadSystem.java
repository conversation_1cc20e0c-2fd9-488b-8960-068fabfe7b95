package io.nue.download;

import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.PriorityBlockingQueue;

public class DownloadSystem {
    private Map<String, User> users;
    private PriorityBlockingQueue<DownloadTask> taskQueue;
    private Map<String, Integer> userDownloadCount;
    private ExecutorService executorService;
    private static final int MAX_CONCURRENT_DOWNLOADS = 5;

    public DownloadSystem() {
        users = new HashMap<>();
        taskQueue = new PriorityBlockingQueue<>(11, Comparator.comparingInt(task -> userDownloadCount.getOrDefault(task.getUserId(), 0)));
        userDownloadCount = new ConcurrentHashMap<>();
        executorService = Executors.newFixedThreadPool(MAX_CONCURRENT_DOWNLOADS);
    }

    public void addUser(String userId) {
        users.putIfAbsent(userId, new User(userId));
    }

    public void addFileToUser(String userId, String fileId) {
        User user = users.get(userId);
        if (user != null) {
            user.addFile(fileId);
        }
    }

    public void queueDownload(String userId, String fileId) {
        taskQueue.offer(new DownloadTask(userId, fileId));
    }

    public void startDownloading() {
        while (true) {
            try {
                DownloadTask task = taskQueue.take();
                executorService.submit(() -> {
                    downloadFile(task);
                    userDownloadCount.merge(task.getUserId(), 1, Integer::sum);
                });
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }

    private void downloadFile(DownloadTask task) {
        System.out.println("Downloading file " + task.getFileId() + " for user " + task.getUserId());
        // Simulate download time
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    public void shutdown() {
        executorService.shutdown();
    }

}
