package io.nue;

import static com.ruby.commons.RubySessionHeaders.RUBY_TID;
import static com.ruby.commons.RubySessionHeaders.RUBY_UID;

import com.google.common.collect.Lists;
import io.github.bonigarcia.wdm.WebDriverManager;
import io.nue.pdf.config.ChromeOptionsConfig;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.openqa.selenium.By;
import org.openqa.selenium.ElementNotInteractableException;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.devtools.DevTools;
import org.openqa.selenium.devtools.v126.browser.Browser;
import org.openqa.selenium.devtools.v126.network.Network;
import org.openqa.selenium.devtools.v126.network.model.Headers;
import org.openqa.selenium.devtools.v126.page.Page;
import org.openqa.selenium.support.ui.ExpectedCondition;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;

@Ignore
public class WebDriverTest {

    private static final Logger log = LoggerFactory.getLogger(WebDriverTest.class);
    private static final String BLANK = "about:blank";
    private final String url1 = "https://app.test.nue.io/view-invoice/BLafrtoaFdLJGxk0JbejbkNdjhPRgJ83NXTRNV0Ayk6D35_iwG6lfkQnKFbnTsAEds3Gz_mKrOSbu0qa03uzgo6kKonNx_hgnNqVYI-hnjpKPzhL6OF506iyIX0buMzSw834MZWr7Wk1BXBTy5PRY1g3ZQwXk4DVhYnW3ZIXinXojnnxKv9HHoOi7zwsXHtkxx4RiaehM6tXWjhQtuDHeIW";
    private final String url2 = "https://app.perftest.nue.io/view-invoice/BCwnezv6Llfa2KncItooc9v3AU-HmNASJ2STY90GT1S91hgebhlq9RPGYibZykv6V76rQ2OU3Ds7q0AYg_65Uc4BZEkmfA-FPgnF8X3ouMI9BEt9ocps1Scx4ub_sgXzwPG1xeGi4HccFGiWGLfwD8d0fYtCoDf27S8WVa6dnln2Q==";
    private final String url3 = "http://localhost:3000/view-invoice/BNO7MDMODnUtuGDgW7uKAxP57Sxjo4Hfw-i0GX0sObNJvLxg20zG9XpKuVdcuuWJk9nkDseY3ArvA0BRKomwujQwqIK8XKWQqKaQMIDWZQQUOraLzdeC0bXDxxz-UCbt42uc4dLOoPwxVc8gnLu91oGGXOMKCp1ZuQ51yuHGZnVI5T-UEhUQOVsAot9eQ8-pVi6yBL9Sj_9ufAAG1B7Bppe";

    private final ConcurrentMap<String, String> urlHandlerMap = new ConcurrentHashMap<>();
    private final String newBlankTabJs = "window.open('" + BLANK + "', '_blank');";
    private final String downloadDirectory = System.getProperty("user.home") + "/Downloads";
    private WebDriverConfig config = null;
    private ChromeOptionsConfig chromeOptionsConfig = null;
    private ChromeDriver chromeDriver = null;
    private WebDriverWait chromeDriverWait = null;
    private ThreadPoolExecutor threadPoolExecutor = null;

    @Before
    public void setUp() {
        chromeOptionsConfig = new ChromeOptionsConfig();
        chromeOptionsConfig.setChromeHeadless(false);

        config = new WebDriverConfig();
        config.setChromeOptions(chromeOptionsConfig.chromiumOptions());
        chromeDriver = (ChromeDriver) config.webDriver();
        chromeDriverWait = config.webDriverWait();
        WebDriverManager.chromiumdriver().browserVersion("126.0.6478.0").setup();
        threadPoolExecutor = new ThreadPoolExecutor(12, 12, 300L, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>());
    }

    @Test
    public void testWebDriver() {
        CompletableFuture<String> currentHandler = CompletableFuture.supplyAsync(() -> {
            String js = "window.open('" + BLANK + "', '_blank');";
            chromeDriver.executeScript(js);
            Object[] allHandlers = chromeDriver.getWindowHandles().toArray();
            String newHandle = allHandlers[1].toString();
            log.info("New window handle: " + newHandle);
            return newHandle;
        });

        CompletableFuture<WebElement> downloadButton = currentHandler.thenApplyAsync(handle -> {
            chromeDriver.switchTo().window(handle);
            chromeDriver.get(url1);
            // Wait for the getInvoiceById method to finish
            chromeDriverWait.withTimeout(Duration.ofSeconds(10))
                    .pollingEvery(Duration.ofMillis(300))
                    .ignoring(ElementNotInteractableException.class);

            WebElement result = chromeDriverWait.until(ExpectedConditions.presenceOfElementLocated(By.cssSelector("rect#download")));
            result.click();
            log.info("Download button clicked");
            //chromeDriver.executeCdpCommand();
            try {
                Thread.sleep(60000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            return result;
        });

        CompletableFuture<ResponseEntity<InputStreamResource>> pdf = downloadButton.handle((result, error) -> {
            File pdfFile = findLatestPdfFile(downloadDirectory);
            log.info("Latest PDF file: " + pdfFile.getName());
            try {
                return downloadPdf(pdfFile);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });

        pdf.join();
    }

    @Test
    public void testWebDriverMultiThread() {
        List<String> tasks = Lists.newArrayList(url1); // hiddenIndicator  url2
        List<Future<String>> futureList = new ArrayList<>(2);
        for (String url : tasks) {
            Future<String> handlerFuture = threadPoolExecutor.submit(() -> {
                String newHandle = openTabAndFocus(url, null);
                urlHandlerMap.put(url, newHandle);

                log.info("{} New window handle: {}", url, newHandle);
                return newHandle;
            });
            futureList.add(handlerFuture);
        }

        for (Future<String> future : futureList) {
            String newHandle = null;
            try {
                newHandle = future.get();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                throw new RuntimeException(e);
            }
            log.info("Switch to New window handle: {}", newHandle);
            String tabId = newHandle;
            File pdf = downloadPdf(newHandle, url -> {
                log.info("Downloading PDF for tab: {} have finished!", tabId);
                File pdfFile = findLatestPdfFile(downloadDirectory);
                log.info("Latest PDF file: " + pdfFile.getName());

                return pdfFile;
            });

            System.out.println(pdf);

            chromeDriver.quit();
        }
    }

    @Test
    public void testWebDriverWithHeaders(){
        String url = "http://localhost:3000/invoice-pdf/9d94a857-6872-4473-8d13-f24ba774b9eb";
        Map<String, Object> headers = new ConcurrentHashMap<>();
        headers.put(HttpHeaders.ACCEPT, "application/json, text/plain, */*");
        headers.put(HttpHeaders.ACCEPT_ENCODING, "gzip, deflate, br");
        headers.put(HttpHeaders.CONNECTION, "keep-alive");
        headers.put(HttpHeaders.COOKIE, "Idea-f08acbfc=94044319-0125-430f-9278-a19d0bcc2428; JSESSIONID=BCCA748C023EB0007E36821F180B78A9; ruby-access-key=****************************************************************************************; ruby-refresh-token=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; ruby-exp-in=900; ruby-access-token=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" );
        headers.put(HttpHeaders.USER_AGENT, "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36");
        headers.put(HttpHeaders.ACCEPT_LANGUAGE, "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7");

        headers.put("Sec-Ch-Ua","\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\"");
        headers.put("Sec-Ch-Ua-Mobile","?0");
        headers.put("Sec-Ch-Ua-Platform","macOS");
        headers.put("Sec-Fetch-Dest","empty");
        headers.put("Sec-Fetch-Mode","cors");
        headers.put("Sec-Fetch-Site","same-origin");
        headers.put("sec-fetch-user","?1");

        String newHandle = openTabAndFocus(url, headers);
        
        System.out.println(newHandle);
    }

    public File downloadPdf(String newHandle, Function<String, File> pdfFileDownloader) {
        synchronized (chromeDriver) {
            chromeDriver.switchTo().window(newHandle);
            // Wait for the getInvoiceById method to finish
            chromeDriverWait.withTimeout(Duration.ofSeconds(10))
                    .pollingEvery(Duration.ofMillis(300))
                    .ignoring(ElementNotInteractableException.class);

            WebElement result = chromeDriverWait.until(ExpectedConditions.presenceOfElementLocated(By.cssSelector("rect#download")));
            DevTools devTools = chromeDriver.getDevTools();
            devTools.createSession();
            devTools.send(Page.enable());

            log.info("Download button clicked");
            result.click();

            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            //1.file name                
            devTools.addListener(Browser.downloadWillBegin(), event -> {
                try {
                    String guid = event.getGuid();
                    String filename = event.getSuggestedFilename();
                    log.info("Tab::{}, is downloading: {}", guid, filename);
                } catch (Exception e) {
                    System.out.println("Error while downloading file");
                }
            });
            
            AtomicBoolean downloadComplete = new AtomicBoolean(false);
            devTools.addListener(Browser.downloadProgress(), progress -> {
                if (progress.getState().toString().equals("completed")) {
                    downloadComplete.set(true);
                    log.info("Download completed for tab: {}", newHandle);
                }
            });

            chromeDriverWait.withTimeout(Duration.ofSeconds(10))
                    .pollingEvery(Duration.ofMillis(300))
                    .ignoring(ElementNotInteractableException.class);
            chromeDriverWait.until((ExpectedCondition<Boolean>) webDriver -> downloadComplete.get());
        }

        return null;
    }

    public String openTabAndFocus(String url, Map<String, Object> headers) {
        synchronized (chromeDriver) {
            Set<String> allHandlers = chromeDriver.getWindowHandles();
            int currentHandlerCount = allHandlers.size();
            log.info(" Thread {}, currentHandlerCount :{}", Thread.currentThread().getId(), currentHandlerCount);
            chromeDriver.executeScript(newBlankTabJs);
            allHandlers = chromeDriver.getWindowHandles();
            log.info(" Thread {}, NEW currentHandlerCount :{}", Thread.currentThread().getId(), allHandlers.size());
            String newHandle = getAllHandles()[currentHandlerCount].toString();
            chromeDriver.switchTo().window(newHandle);
            if (chromeDriver.getCurrentUrl().startsWith(BLANK)) {
                if (headers != null) {
                    DevTools devTools = chromeDriver.getDevTools();
                    devTools.createSession();

                    devTools.send(Network.enable(Optional.empty(), Optional.empty(), Optional.empty()));
                    Headers devToolsHeaders = new Headers(headers);
                    devTools.send(Network.setExtraHTTPHeaders(devToolsHeaders));
                }
                chromeDriver.get(url);

                return newHandle;
            }
        }

        throw new RuntimeException("new tab not found...");
    }

    public Object[] getAllHandles() {
        return chromeDriver.getWindowHandles().toArray();
    }

    private File findLatestPdfFile(String downloadDirectory) {
        File downloadDir = new File(downloadDirectory);
        File[] files = downloadDir.listFiles((dir, name) -> name.endsWith(".pdf"));
        if (files == null || files.length == 0) {
            return null;
        }

        return Arrays.stream(files)
                .max((f1, f2) -> Long.compare(f1.lastModified(), f2.lastModified()))
                .orElse(null);
    }

    public ResponseEntity<InputStreamResource> downloadPdf(File pdfFile) throws IOException {
        // Specify the path to the local PDF file
        FileInputStream inputStream = new FileInputStream(pdfFile);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentLength(pdfFile.length());
        headers.setContentDispositionFormData("attachment", pdfFile.getName());

        return new ResponseEntity<>(new InputStreamResource(inputStream), headers, HttpStatus.OK);
    }

    @Test
    public void pathFile() {
        String path = downloadDirectory + "/" + "INV-00004096 ().pdf";
        Path filePath = Paths.get(path);
        System.out.println(filePath.toFile().exists());
        System.out.println(filePath.getFileName().toString());
    }
}
