<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProperty name="HibernateSQLLevel" source="logging.level.hibernate.SQL"
                    defaultValue="WARN"/>
    <springProperty name="HibernateSQLBasicBinderLevel" source="logging.level.hibernate.basicBinder"
                    defaultValue="WARN"/>
    <property name="logging.path" value="${LOG_PATH:-.}/logs/apps"/>
    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>

    <conversionRule conversionWord="bmdc" converterClass="ch.qos.logback.classic.pattern.MDCConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>

    <property name="CONSOLE_LOG_PATTERN"
              value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %clr(%bmdc){magenta} %m%n%wEx{full}}"/>
    <property name="FILE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %5p --- [%15.15t] %-40.40logger{39} : %bmdc %m%n%wEx{full}}"/>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logging.path}/pdf.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logging.path}/pdf-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>15</maxHistory>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <logger name="org.apache.catalina" level="WARN"/>
    <logger name="org.apache.tomcat" level="WARN"/>
    <logger name="org.hibernate" level="WARN"/>
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.hibernate.SQL" level="${HibernateSQLLevel:-WARN}"/>
    <logger name="org.hiberate.type.descriptor.sql.BasicBinder" level="${HibernateSQLBasicBinderLevel:-WARN}"/>

    <springProfile name="dev">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            </encoder>
        </appender>
    </springProfile>
    <springProfile name="!dev">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="net.logstash.logback.encoder.LogstashEncoder">
                <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            </encoder>
        </appender>
    </springProfile>
    <root level="${root.log.level:-info}">
        <appender-ref ref="FILE"/>
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>