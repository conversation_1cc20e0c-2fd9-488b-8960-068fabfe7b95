server:
  port: 5300
logging:
  format: "${LOG_FORMAT:json}"
  pattern:
    console: "%d{HH:mm:ss.SSS} %-5level > %msg - %thread{8} %logger{32} %X%n"
  level:
    root: INFO
    org.springframework: "${LOG_LEVEL_SPRING:INFO}"
    io.nue: "${LOG_LEVEL_NUE:DEBUG}"
selenium:
  browser:
    type: chromium
    main-version: "126.0."
    headless: false
    numbers: ${BROWSER_NUMBERS:3}
    dev-version: "126.0.6478.0"
    enable-logging: "${BROWSER_LOGGING:false}"

pdf-generator:
  executor:
    core-pool-size: "${BROWSER_NUMBERS:2}"
  web-driver-pool:
    wait-timeout-seconds: 10

auth-app:
  url: "${AUTH_APP_ENDPOINT:http://127.0.0.1:5050}"
app:
  root-url: "${APP_ROOT_URL:http://localhost:3000}"