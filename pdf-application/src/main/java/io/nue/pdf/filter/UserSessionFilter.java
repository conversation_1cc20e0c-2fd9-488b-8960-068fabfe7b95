package io.nue.pdf.filter;

import static com.google.common.base.Strings.isNullOrEmpty;
import static com.ruby.commons.RubySessionHeaders.RUBY_CORRELATION_ID;
import static com.ruby.commons.RubySessionHeaders.RUBY_CURRENCY;
import static com.ruby.commons.RubySessionHeaders.RUBY_LOCALE;
import static com.ruby.commons.RubySessionHeaders.RUBY_TID;
import static com.ruby.commons.RubySessionHeaders.RUBY_UID;
import static com.ruby.commons.RubySessionHeaders.RUBY_ZONE;
import static org.springframework.util.StringUtils.isEmpty;

import com.ruby.commons.RubyException;
import com.ruby.commons.UserSessionContext;
import com.ruby.commons.UserSessionContextHolder.UserSessionContextClosable;
import com.ruby.commons.UserSessionRunner;

import io.nue.util.log.LogUtils;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UrlPathHelper;

import java.io.IOException;
import java.time.ZoneId;
import java.util.Locale;

@Component
@Order(3)
public class UserSessionFilter implements Filter {

    private static final String[] BYPASS_PATH_LIST = {"/login", "/reLogin", "/auth", "/health", "/v3/api-docs", "/magiclink-pdf"};
    public static final String CURRENCY_USD = "USD";

    private final UrlPathHelper urlPathHelper;

    @Autowired
    public UserSessionFilter() {
        this.urlPathHelper = new UrlPathHelper();
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        LogUtils.clearMdc();
        try {
            filter(request, response, chain);
        } finally {
            LogUtils.clearMdc();
        }
    }

    private void filter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        String userId = httpRequest.getHeader(RUBY_UID);
        String tenantId = httpRequest.getHeader(RUBY_TID);
        String locale = httpRequest.getHeader(RUBY_LOCALE);
        String zoneId = httpRequest.getHeader(RUBY_ZONE);
        String currency = httpRequest.getHeader(RUBY_CURRENCY);
        String correlationId = httpRequest.getHeader(RUBY_CORRELATION_ID);

        if (userId != null && userId.length() > 0 && tenantId != null && tenantId.length() > 0) {
            UserSessionContext sessionContext = UserSessionContext.newUserSession(tenantId, userId, toJavaLocale(locale), toJavaZoneId(zoneId));
            sessionContext.setCurrencyCode(!isNullOrEmpty(currency) ? currency : CURRENCY_USD);
            try (UserSessionContextClosable contextClosable = UserSessionRunner.setClosableContext(sessionContext)) {
                LogUtils.setMdcBySession(sessionContext);

                if (correlationId != null && !correlationId.isEmpty()) {
                    LogUtils.setCorrelationId(correlationId);
                } else {
                    LogUtils.generateCorrelationId();
                }

                chain.doFilter(request, response);
            }
        } else {
            String requestPath = urlPathHelper.getPathWithinApplication(httpRequest);
            if (isByPassPath(requestPath)) {
                chain.doFilter(request, response);
            } else {
                httpResponse.setStatus(HttpStatus.UNAUTHORIZED.value());
            }
        }
    }
    
    private boolean isByPassPath(String requestPath) {
        for (String byPassPath : BYPASS_PATH_LIST) {
            if (requestPath.startsWith(byPassPath) || requestPath.equals("swagger")) {
                return true;
            }
        }
        return false;
    }

    private static Locale toJavaLocale(String localeStr) {
        try {
            return isEmpty(localeStr) ? Locale.getDefault() : Locale.forLanguageTag(localeStr);
        } catch (Exception e) {
            throw new RubyException("Invalid locale " + localeStr, e);
        }
    }

    private static ZoneId toJavaZoneId(String zoneStr) {
        try {
            return isEmpty(zoneStr) ? ZoneId.systemDefault() : ZoneId.of(zoneStr);
        } catch (Exception e) {
            throw new RubyException("Invalid zone id " + zoneStr, e);
        }
    }

    public static void main(String[] args) {
        System.out.println(Locale.forLanguageTag("en-US"));
        System.out.println(Locale.forLanguageTag("en"));
        System.out.println(Locale.CANADA.toString());
    }
}