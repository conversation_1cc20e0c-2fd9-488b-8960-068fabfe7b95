package io.nue.pdf.filter;

import static com.google.common.base.Strings.isNullOrEmpty;
import static com.ruby.commons.LogConstants.LOG_ATTR_CLIENT;
import static com.ruby.commons.LogConstants.LOG_ATTR_END_ON;
import static com.ruby.commons.LogConstants.LOG_ATTR_EXEC_TIME;
import static com.ruby.commons.LogConstants.LOG_ATTR_METHOD;
import static com.ruby.commons.LogConstants.LOG_ATTR_PATH;
import static com.ruby.commons.LogConstants.LOG_ATTR_RID;
import static com.ruby.commons.LogConstants.LOG_ATTR_START_ON;
import static com.ruby.commons.LogConstants.LOG_ATTR_STATUS;
import static com.ruby.commons.RubySessionHeaders.RUBY_CORRELATION_ID;

import com.ruby.commons.ProcessIDGenerator;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.web.util.UrlPathHelper;

import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;


//@Component
//@Order(2)
public class LoggingFilter implements Filter {

    private static final Logger logger = LoggerFactory.getLogger("RubyRequestLogger");
    public static final String REQUEST_ATTR_ERROR = "Error";

    private final UrlPathHelper urlPathHelper;

    public LoggingFilter() {
        this.urlPathHelper = new UrlPathHelper();
    }


    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        String requestPath = urlPathHelper.getPathWithinApplication(httpRequest);
        if (requestPath.startsWith("/health")) {
            try {
                chain.doFilter(request, response);
                return;
            } catch (Exception e) {
                logger.error("health check error.", e);
                throw e;
            }
        }
        try {
            MDC.put(LOG_ATTR_METHOD, httpRequest.getMethod());
            MDC.put(LOG_ATTR_PATH, httpRequest.getServletPath());
            MDC.put(LOG_ATTR_CLIENT, httpRequest.getRemoteAddr());
            String correlationId = !isNullOrEmpty(httpRequest.getHeader(RUBY_CORRELATION_ID)) ? httpRequest.getHeader(RUBY_CORRELATION_ID) : ProcessIDGenerator.generate();
            MDC.put(LOG_ATTR_RID, correlationId);
            MDC.put(LOG_ATTR_STATUS, "PROCESS");
            MDC.put(LOG_ATTR_START_ON, LocalDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME));
            chain.doFilter(request, response);
        } catch (Exception e) {
            request.setAttribute(REQUEST_ATTR_ERROR, e);
            throw e;
        } finally {
            try {
                LocalDateTime startTime = LocalDateTime.parse(MDC.get(LOG_ATTR_START_ON), DateTimeFormatter.ISO_DATE_TIME);
                LocalDateTime endTime = LocalDateTime.now();
                MDC.put(LOG_ATTR_END_ON, endTime.format(DateTimeFormatter.ISO_DATE_TIME));
                MDC.put(LOG_ATTR_EXEC_TIME, String.valueOf(Duration.between(startTime, endTime).toMillis()));
                if (request.getAttribute(REQUEST_ATTR_ERROR) != null) {
                    Exception ex = (Exception) request.getAttribute("Error");
                    MDC.put(LOG_ATTR_STATUS, "ERROR");
                    logger.error("Exceptions found.", ex);
                } else {
                    MDC.put(LOG_ATTR_STATUS, "END");
                    logger.info("Request finished.");
                }
            } finally {
                request.removeAttribute(REQUEST_ATTR_ERROR);
                MDC.clear();
            }
        }
    }
}
