package io.nue.pdf;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.servers.Server;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;


@OpenAPIDefinition(
        info = @Info(title = "LIFECYCLE MANAGER", version = "1.0"),
        servers = {@Server(url = "https://api.nue.io/v1", description = "Nue API Endpoint")}
)
@SpringBootApplication(scanBasePackages = "io.nue.pdf", exclude = {DataSourceAutoConfiguration.class})
public class PdfApplication {

    private static final Logger log = LoggerFactory.getLogger(PdfApplication.class);
    public static void main(String[] args) {
        java.security.Security.setProperty("networkaddress.cache.ttl", "60");
        SpringApplication.run(PdfApplication.class, args);
        log.info("\033[0;33mPdfApplication started successfully！\033[0m");
    }
}
