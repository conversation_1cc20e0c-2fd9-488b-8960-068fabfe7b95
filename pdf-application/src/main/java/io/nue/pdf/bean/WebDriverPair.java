package io.nue.pdf.bean;

import io.nue.exception.NueException;
import io.nue.pdf.utils.MemoryUtils;
import org.openqa.selenium.By;
import org.openqa.selenium.ElementNotInteractableException;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.devtools.Command;
import org.openqa.selenium.devtools.DevTools;
import org.openqa.selenium.devtools.v126.network.Network;
import org.openqa.selenium.devtools.v126.page.Page;
import org.openqa.selenium.devtools.v126.page.Page.PrintToPDFResponse;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;

public class WebDriverPair {
    private static final Logger log = LoggerFactory.getLogger(WebDriverPair.class);

    public static final String BLANK = "about:blank";
    final static String newBlankTabJs = "window.open('" + BLANK + "', '_blank');";
    private final static String DOWNLOAD_BUTTON_SELECTOR = "div#magiclink-downloadpdf-hidden";
    private final static int MAX_CALLS = 30;
    private final static int MAX_ERROR_COUNT = 20;
    private final int index;
    private String defaultHandler;
    private String handler;
    private String magiclink;
    private String fileName;
    private final ChromeOptions chromeOptions;
    private ChromeDriver webDriver;
    private Integer chromeDriverProcessId;
    private final AtomicInteger counter = new AtomicInteger(0);
    private final AtomicInteger failureCounter = new AtomicInteger(0);

    // Map<id, procesId> 
    public WebDriverPair(int index, ChromeOptions chromeOptions) {
        this.index = index;
        this.chromeOptions = chromeOptions;
        reconnect();
    }

    /**
     * Download the PDF file using DevTools API.
     */
    public synchronized ResponseEntity<?> downloadPdf() {
        if (StringUtils.hasLength(magiclink)) {
            //
            openTabAndFocus();
            //
            return invokePdfGeneration();
        }

        return null;
    }

    private synchronized void openTabAndFocus() {
        Assert.hasText(magiclink, "Magiclink should not be empty!");
        log.info(" Thread {}, opening new tab for url :{}", Thread.currentThread().threadId(), magiclink);
        //If idle for a while or host goes to sleep, this will occur an error.
        checkAndReconnect();

        counter.incrementAndGet();
        try {
            Set<String> currentHandlers = webDriver.getWindowHandles();
            log.info("Thread {}, currentHandlers :{}", Thread.currentThread().threadId(), currentHandlers.size());
            //new tab
            webDriver.executeScript(newBlankTabJs);
            Set<String> handlers = webDriver.getWindowHandles();
            log.info("Thread {}, after new tab handlers :{}", Thread.currentThread().threadId(), handlers.size());
            // new tab successfully opened
            handlers.removeAll(currentHandlers);
            this.handler = handlers.iterator().next();
            if (StringUtils.hasLength(this.handler)) {
                webDriver.switchTo().window(handler);
                long getStart = System.currentTimeMillis();
                webDriver.get(magiclink + "?internal=true");
                log.info("Spend {} milliseconds to open magic link :{}", System.currentTimeMillis() - getStart, magiclink);    
            } else {
                throw new NueException("NO_NEW_TAB_ERROR", "No new tab opened for url " + magiclink);
            }
        } catch (Exception e) {
            int failures = failureCounter.incrementAndGet();
            log.error("Thread " + Thread.currentThread().threadId() + ", Error opening new tab for url " + magiclink
                    + "[index]= " + index + " failure times: " + failures, e);
            closeCurrentTab();

            throw new NueException("DRIVER_GET_ERROR","Error opening new tab for url " + magiclink, e);
        }
    }

    private ResponseEntity<?> invokePdfGeneration() {
        Assert.hasText(handler, "handler should not be empty!");

        WebDriverWait webDriverWait = new WebDriverWait(webDriver, Duration.ofSeconds(120));
        webDriverWait.withMessage(new Supplier<String>() {
            @Override
            public String get() {
                return String.format(
                        "Thread %d, Timeout waiting for download button in tab: %s. Current URL: %s",
                        Thread.currentThread().threadId(), handler, webDriver.getCurrentUrl()
                );
            }
        });

        webDriverWait.pollingEvery(Duration.ofMillis(1000)).ignoring(ElementNotInteractableException.class);
        try (DevTools devTools = webDriver.getDevTools();){
            // button to download by backend
            WebElement result = webDriverWait.until(ExpectedConditions.presenceOfElementLocated(By.cssSelector(DOWNLOAD_BUTTON_SELECTOR)));
            log.info("Thread {}, Download button found in tab: {}", Thread.currentThread().threadId(), handler);
            String fileName = result.getAttribute("role-name");
            log.info("Thread {}, get fileName after download btn render finished: {}", Thread.currentThread().threadId(), fileName);
            devTools.createSession(handler);
            devTools.send(Page.enable());
            devTools.getDomains().log().enable();

            byte[] pdfBytes = printPdf(devTools);
            
            log.info("Thread {}, pdf download succeeded for file :{}, handle:{}", Thread.currentThread().threadId(), fileName, handler);
            ResponseEntity<byte[]> pdfResponse = createPdfResponse(pdfBytes, fileName);
            //
            failureCounter.set(0);
            log.info("Thread {}, return file name :{}, handle:{}", Thread.currentThread().threadId(), fileName, handler);

            return pdfResponse;
        } catch (Exception e) {
            int failures = failureCounter.incrementAndGet();
            log.error("Thread" + Thread.currentThread().threadId() + ", Download failed for tab: " + handler
                    + ",[index]= " + index + " failures: " + failures, e);
            throw new NueException("FILE_DOWNLOAD_ERROR", "Download failed for tab: " + handler, e);
        } finally {
            log.info("Thread {}, Closing tab: {}", Thread.currentThread().threadId(), handler);
            closeCurrentTab();
            webDriverWait = null;
        }
    }

    private byte[] printPdf(DevTools devTools) {
        // 4. print PDF parameters
        Command<PrintToPDFResponse> pdfParams = Page.printToPDF(
                java.util.Optional.of(true),            // landscape - Landscape mode
                java.util.Optional.of(false),           // displayHeaderFooter - No headers/footers
                java.util.Optional.of(true),           // printBackground - No background
                java.util.Optional.of(1.0),             // scale - 100% scale
                java.util.Optional.of(11.7),            // paperWidth - A4 landscape width
                java.util.Optional.of(8.27),            // paperHeight - A4 landscape height
                java.util.Optional.of(0.4),             // marginTop - Default margin
                java.util.Optional.of(0.4),             // marginBottom - Default margin
                java.util.Optional.of(0.4),             // marginLeft - Default margin
                java.util.Optional.of(0.4),             // marginRight - Default margin
                java.util.Optional.empty(),             // pageRanges - Print all pages
                java.util.Optional.of(""),              // headerTemplate - Empty header
                java.util.Optional.of(""),              // footerTemplate - Empty footer
                java.util.Optional.of(false),           // preferCSSPageSize - Use specified paper size
                java.util.Optional.empty(),                     // transferMode - Use default
                java.util.Optional.of(false),           // generateTaggedPDF - Not needed
                java.util.Optional.of(false)            // generateDocumentOutline - Not needed
        );

        // 5. Generate PDF with enhanced parameters for large files
        PrintToPDFResponse printToPDFResponse = devTools.send(pdfParams);
        String pdfBase64 = printToPDFResponse.getData();

        // Validate PDF data before processing
        if (pdfBase64 == null || pdfBase64.isEmpty()) {
            throw new RuntimeException("PDF data is empty - page may not be fully loaded");
        }

        // 6. Decode and save PDF file with validation
        byte[] pdfBytes = Base64.getDecoder().decode(pdfBase64);

        return pdfBytes;
    }
    
    private synchronized void checkAndReconnect() {
        if (!isDriverConnected()) {
            try {
                reconnect();
            } catch (Exception e) {
                log.error("Failed to reconnect : ", e);
            }
        }
    }

    public synchronized void reconnect() {
        if (webDriver != null) {
            try {
                webDriver.quit(); // Close the existing session
            } catch (Exception e) {
                log.error("Failed to close existing session!", e);
            }
        }

        // Prevent mem leak
        webDriver = null;
        counter.set(0);
        failureCounter.set(0);
        // Prevent memory leak
        log.info("Create new Chromedriver instance! Chrome Driver {} :{}", index, chromeOptions);

        webDriver = new ChromeDriver(chromeOptions);
        defaultHandler = webDriver.getWindowHandle();
        //
        MemoryUtils.printMemoryUsage();
    }

    public synchronized boolean isDriverConnected() {
        if (webDriver != null) {
            try {
                webDriver.getCurrentUrl();
                return true;
            } catch (Exception e) {
                log.error("Failed to get current url when isDriverConnected(): ", e);
                return false;
            }
        }
        
        return false;
    }

    public synchronized boolean reachMaxCalls() {
        return counter.get() >= MAX_CALLS || failureCounter.get() >= MAX_ERROR_COUNT;
    }
    
    public synchronized void quit() {
        if (webDriver != null) {
            webDriver.quit();
        }
    }

    public synchronized void closeCurrentTab() {
        if (webDriver != null) {
            if (!defaultHandler.equals(webDriver.getWindowHandle())) {
                webDriver.close();// Close the current tab
                focusDefaultTab();
            }
            handler = null;
            magiclink = null;
        }
    }

    public synchronized void focusDefaultTab() {
        webDriver.switchTo().window(defaultHandler);
    }

    public int getIndex() {
        return index;
    }

    public void setMagiclink(String magiclink) {
        this.magiclink = magiclink;
    }
    
    public String getFileName() {
        return fileName;
    }

    /**
     * Convert PDF bytes to ResponseEntity for HTTP response
     *
     * @param pdfBytes The PDF content as byte array
     * @param fileName The filename for the PDF (without .pdf extension)
     * @return ResponseEntity with PDF content and appropriate headers
     */
    public ResponseEntity<byte[]> createPdfResponse(byte[] pdfBytes, String fileName) {
        if (pdfBytes == null || pdfBytes.length == 0) {
            return ResponseEntity.badRequest()
                    .body("PDF generation failed - empty content".getBytes());
        }

        // Validate PDF format
        if (pdfBytes.length < 4 || !new String(pdfBytes, 0, 4).equals("%PDF")) {
            return ResponseEntity.badRequest()
                    .body("Invalid PDF format".getBytes());
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentLength(pdfBytes.length);

        // Download PDF file
        headers.setContentDispositionFormData("attachment", fileName + ".pdf");
        // Add cache control headers
        headers.setCacheControl("no-cache, no-store, must-revalidate");
        headers.setPragma("no-cache");
        headers.setExpires(0);

        log.info("✅ PDF ResponseEntity created: {} bytes, filename: {}.pdf", pdfBytes.length, fileName);

        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(pdfBytes.length)
                .contentType(MediaType.APPLICATION_PDF)
                .body(pdfBytes);
    }

}
