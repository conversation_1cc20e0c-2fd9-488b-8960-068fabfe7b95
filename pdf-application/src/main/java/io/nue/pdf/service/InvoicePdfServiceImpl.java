package io.nue.pdf.service;

import io.nue.exception.NueException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadPoolExecutor;

@Service
public class InvoicePdfServiceImpl implements InvoicePdfService {

    private static final Logger log = LoggerFactory.getLogger(InvoicePdfServiceImpl.class);

    private final SeleniumService seleniumService;
    private final ThreadPoolExecutor threadPoolExecutor;
    private final ConcurrentMap<String, Boolean> urlMap = new ConcurrentHashMap<>();

    public InvoicePdfServiceImpl(SeleniumService seleniumService, ThreadPoolExecutor threadPoolExecutor) {
        this.seleniumService = seleniumService;
        this.threadPoolExecutor = threadPoolExecutor;
    }

    @Override
    public ResponseEntity<?> magicklinkPdf(String magiclinkUrl) {
        try {
            if (urlMap.putIfAbsent(magiclinkUrl, true) != null) {
                log.info("Pdf is generating for magiclinkUrl: " + magiclinkUrl);
                throw new NueException("PDF_IS_GENERATING", "Pdf generation is in progressing, please do not submit repeatedly.");
            }
            Future<ResponseEntity<?>> pdfFuture = threadPoolExecutor.submit(
                    () -> {
                        ResponseEntity<?> pdfResponse = seleniumService.downloadPdfByUrl(magiclinkUrl);
                        return pdfResponse;
                    }
            );
            return pdfFuture.get();
        }catch (InterruptedException e) {
            log.error("InterruptedException while executing task", e);
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            log.error("ExecutionException while executing task", e);
            throw new RuntimeException(e);
        } catch (RejectedExecutionException e) {
            log.error("Task rejected", e);
            throw new NueException("PDF_GENERATION_REJECTED", "Pdf generation task is rejected, please try again later.");
        } finally {
            urlMap.remove(magiclinkUrl);
        }
    }

}
