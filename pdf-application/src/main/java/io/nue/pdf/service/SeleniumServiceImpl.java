package io.nue.pdf.service;

import io.nue.exception.NueException;
import io.nue.pdf.bean.WebDriverPair;
import io.nue.pdf.config.WebDriverPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ThreadPoolExecutor;

@Service
public class SeleniumServiceImpl implements SeleniumService {
    
    private static final Logger log = LoggerFactory.getLogger(SeleniumServiceImpl.class);
    
    private final WebDriverPool webDriverPool;

    public SeleniumServiceImpl(WebDriverPool webDriverPool) {
        this.webDriverPool = webDriverPool;
    }

    @Override
    public ResponseEntity downloadPdfByUrl(String url) {
        Integer index = null;
        WebDriverPair webDriverPair = null;
        try {
            webDriverPair = webDriverPool.acquire();
            index = webDriverPair.getIndex();
        } catch (InterruptedException e) { // will not happen here
            log.error("Thread {}, Failed to acquire web driver from pool", Thread.currentThread().threadId(), e);
            throw new NueException("Failed to acquire web driver from pool", e);
        }

        log.info("Thread {}, Acquired web driver from pool, index: {}", Thread.currentThread().threadId(), index);
        synchronized (webDriverPair) {
            try {
                webDriverPair.setMagiclink(url);
                return webDriverPair.downloadPdf();
            } finally {
                webDriverPool.release(index);
            }
        }
    }
}
