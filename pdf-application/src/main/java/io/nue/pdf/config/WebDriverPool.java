package io.nue.pdf.config;

import io.github.bonigarcia.wdm.WebDriverManager;
import io.nue.exception.NueException;
import io.nue.pdf.bean.WebDriverPair;
import io.nue.pdf.utils.ChromiumUtils;
import io.nue.pdf.utils.MemoryUtils;
import org.openqa.selenium.chrome.ChromeOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import java.nio.file.Path;
import java.util.Optional;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

import javax.annotation.PostConstruct;


@Configuration
public class WebDriverPool {

    private static final Logger log = LoggerFactory.getLogger(WebDriverPool.class);
    private final ArrayBlockingQueue<Integer> availableIndices;
    private final WebDriverPair[] allPairs;
    private final int poolSize;
    private final long waitTimeoutSeconds;
    private final ReentrantLock lock = new ReentrantLock();
    private final ChromeOptions chromeOptions;

    public WebDriverPool(@Value("${pdf-generator.executor.core-pool-size:5}") int size,
            @Value("${pdf-generator.web-driver-pool.wait-timeout-seconds:10}") long waitTimeoutSeconds,
            @Qualifier("chromeOptions") ChromeOptions chromeOptions) {
        this.poolSize = size;
        this.waitTimeoutSeconds = waitTimeoutSeconds;
        this.availableIndices = new ArrayBlockingQueue<>(size);
        this.allPairs = new WebDriverPair[size];
        this.chromeOptions = chromeOptions;
        initializeAllPairs();
    }

    private void initializeAllPairs() {
        String version = chromeOptions.getBrowserVersion();
        if (StringUtils.hasLength(version)){
            Optional<Path> chromedriverPathOpt = ChromiumUtils.getChromedriverPath();
            if (chromedriverPathOpt.isPresent()){
                log.info("Using chromedriver path: {}", chromedriverPathOpt.get());
                System.setProperty("webdriver.chrome.driver", chromedriverPathOpt.get().toString());
            } else {
                log.info("Should download the Chromedriver version: {}", version);
                WebDriverManager.chromiumdriver().browserVersion(version).setup();
                log.info("Downloading chromedriver version: {} complete", version);
            }
        } else {
            System.setProperty("webdriver.chrome.driver", "/usr/bin/chromedriver");
        }

        log.info("Initialising {} Chromedriver instances", poolSize);
        for (int idx = 0; idx < poolSize; idx++) {
            allPairs[idx] = new WebDriverPair(idx, chromeOptions);
            availableIndices.offer(idx);
        }

        MemoryUtils.printJVMMemoryUsage();
    }

    @PostConstruct
    public void shutdownHook() {
        // Shutdown hook
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            if (allPairs != null && allPairs.length > 0) {
                log.info("Shutdown signal detected: Closing opened drivers");
                quitAllDriver();    
                log.info("Opened drivers closed");
            }
        }));

        // Disable driver log output
        System.setProperty("webdriver.chrome.silentOutput", "true");
    }

    public WebDriverPair acquire() throws InterruptedException {
        lock.lock();
        try {
            Integer index = availableIndices.poll(waitTimeoutSeconds, TimeUnit.SECONDS); // Will block if none available
            if (index == null) {
                log.error("Timeout waiting for a free chrome driver: {}{}", waitTimeoutSeconds, TimeUnit.SECONDS);
                throw new NueException("WEBDRIVER_POOL_POLL_TIMEOUT", "Timeout waiting for a free chrome driver");
            }
            return allPairs[index];
        } finally {
            lock.unlock();
        }
    }

    public WebDriverPair getOneDriverInUsing(int idx){
        lock.lock();
        try {
            if (!availableIndices.contains(idx)) {
                return allPairs[idx];
            }
        } finally {
            lock.unlock();
        }

        return null;
    }

    /**
     * Release the driver back to the pool.
     * If the driver reach max calls, it will be re-initialised.
     * 
     * @param index
     * 
     */
    public void release(Integer index) {
        lock.lock();
        try {
            WebDriverPair release = allPairs[index];
            if (release != null){
                if(release.reachMaxCalls()){
                    try {
                        release.quit();
                    } catch (Exception e) {
                        log.error("WebDriver quit failed: ", e);
                    } finally {
                        release.reconnect();
                    }
                }
                availableIndices.offer(index);
            }
        } finally {
            lock.unlock();
        }
    }
    
    public void quitAllDriver() {
        lock.lock();
        try {
            availableIndices.clear();
            for (WebDriverPair pair : allPairs) {
                try {
                    pair.quit();
                } catch (Exception e) {
                    log.error("quitAllDriver quit failed: ", e);
                }
            }
        } finally {
            lock.unlock();
        }
    }

}
