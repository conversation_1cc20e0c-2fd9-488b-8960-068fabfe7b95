package io.nue.pdf.config;

import static io.swagger.v3.oas.models.security.SecurityScheme.Type.HTTP;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class AppConfiguration {

    @Bean
    public OpenAPI customOpenAPI() {
        OpenAPI bean = new OpenAPI().components(
                new Components()
                        .addSecuritySchemes("bearerAuth", new SecurityScheme().type(HTTP).scheme("bearer").bearerFormat("JWT"))
                        .addSecuritySchemes("basicAuth", new SecurityScheme().type(HTTP).scheme("basic"))
        );
        return bean;
    }

    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder restTemplateBuilder) {
        return restTemplateBuilder
                .setConnectTimeout(Duration.ofSeconds(30))
                .setReadTimeout(Duration.ofSeconds(30)).build();
    }

    @Bean(name = "pdfExecutor")
    public ThreadPoolExecutor pdfTaskExecutor(@Value("${pdf-generator.executor.core-pool-size:5}") int corePoolSize) {
        ThreadPoolExecutor t = new ThreadPoolExecutor(corePoolSize, corePoolSize, 300, TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(corePoolSize), 
                Executors.defaultThreadFactory()); //default Policy is AbortPolicy
        t.allowCoreThreadTimeOut(true);

        return t;
    }
}
