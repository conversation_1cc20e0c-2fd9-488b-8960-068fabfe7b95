package io.nue.pdf.config;

import io.github.bonigarcia.wdm.WebDriverManager;
import io.nue.pdf.utils.ChromiumUtils;
import io.nue.pdf.utils.JsonUtils;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.logging.LogType;
import org.openqa.selenium.logging.LoggingPreferences;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.logging.Level;

/**
 * 优化的Chrome选项配置类
 * 专为PDF打印功能设计，提供高性能、稳定、安全的Chrome配置
 * 
 * 主要优化点：
 * 1. 安全性：禁用不必要的功能，防止安全漏洞
 * 2. 性能：优化内存使用，提高渲染速度
 * 3. 稳定性：避免崩溃和挂起问题
 * 4. PDF打印：专门针对PDF生成优化
 */
@Configuration
public class OptimizedChromeOptionsConfig {

    private static final Logger log = LoggerFactory.getLogger(OptimizedChromeOptionsConfig.class);
    
    @Value("${selenium.browser.main-version}")
    private String mainChromiumVersion;

    @Value("${selenium.browser.dev-version}")
    private String devChromiumVersion;
    
    @Value("${selenium.browser.headless:true}")
    private Boolean chromeHeadless;

    @Value("${selenium.browser.enable-logging:false}")
    private Boolean chromeLogging;
    
    @Value("${selenium.browser.window-width:1920}")
    private Integer windowWidth;
    
    @Value("${selenium.browser.window-height:1080}")
    private Integer windowHeight;
    
    @Value("${selenium.browser.page-load-timeout:30}")
    private Integer pageLoadTimeout;
    
    @Bean("optimizedChromeOptions")
    public ChromeOptions chromiumOptions() {
        ChromeOptions options = createOptimizedOptions();

        Map<String, String> chromiumMap = ChromiumUtils.findChromiumBinary(mainChromiumVersion);
        log.info("chromiumOptions chromeBinary:{}", JsonUtils.serialize(chromiumMap));
        
        if (chromiumMap != null && !chromiumMap.isEmpty()) {
            chromiumMap.entrySet().forEach(entry -> {
                options.setBinary(entry.getKey());
                options.setBrowserVersion(entry.getValue());
            });
        }

        setupChromeDriver(options);
        
        log.info("Optimized Chrome options configured successfully");
        return options;
    }

    /**
     * 创建优化的Chrome选项配置
     */
    private ChromeOptions createOptimizedOptions() {
        ChromeOptions options = new ChromeOptions();

        // ========== 核心安全和稳定性配置 ==========
        addSecurityOptions(options);
        
        // ========== PDF打印专用配置 ==========
        addPdfPrintingOptions(options);
        
        // ========== 窗口和显示配置 ==========
        addDisplayOptions(options);
        
        // ========== 性能优化配置 ==========
        addPerformanceOptions(options);
        
        // ========== 渲染优化配置 ==========
        addRenderingOptions(options);
        
        // ========== 网络和加载优化 ==========
        addNetworkOptions(options);
        
        // ========== 禁用不必要功能 ==========
        addDisableOptions(options);
        
        // ========== 设置首选项 ==========
        addPreferences(options);
        
        // ========== 日志配置 ==========
        configureLogging(options);
        
        // ========== Headless模式配置 ==========
        configureHeadlessMode(options);

        return options;
    }

    /**
     * 安全和稳定性配置
     */
    private void addSecurityOptions(ChromeOptions options) {
        options.addArguments("--no-sandbox"); // 容器环境必需
        options.addArguments("--disable-dev-shm-usage"); // 防止共享内存不足
        options.addArguments("--disable-gpu"); // 避免GPU兼容性问题
        options.addArguments("--remote-allow-origins=*"); // 允许远程连接
        options.addArguments("--disable-web-security"); // 禁用同源策略
        options.addArguments("--disable-features=VizDisplayCompositor"); // 提高稳定性
        options.addArguments("--disable-blink-features=AutomationControlled"); // 隐藏自动化标识
    }

    /**
     * PDF打印专用配置
     */
    private void addPdfPrintingOptions(ChromeOptions options) {
        options.addArguments("--enable-print-preview"); // 启用打印预览
        options.addArguments("--kiosk-printing"); // 静默打印模式
        options.addArguments("--print-to-pdf"); // 启用PDF打印
        options.addArguments("--run-all-compositor-stages-before-draw"); // 确保渲染完成
        options.addArguments("--disable-print-preview"); // 禁用打印对话框
        options.addArguments("--enable-precise-memory-info"); // 精确内存信息
    }

    /**
     * 窗口和显示配置
     */
    private void addDisplayOptions(ChromeOptions options) {
        options.addArguments(String.format("--window-size=%d,%d", windowWidth, windowHeight));
        options.addArguments("--start-maximized"); // 最大化窗口
        options.addArguments("--force-device-scale-factor=1"); // 固定缩放比例
        options.addArguments("--high-dpi-support=1"); // 高DPI支持
        options.addArguments("--force-color-profile=srgb"); // 颜色一致性
        options.addArguments("--disable-lcd-text"); // 禁用LCD文本渲染
    }

    /**
     * 性能优化配置
     */
    private void addPerformanceOptions(ChromeOptions options) {
        options.addArguments("--max_old_space_size=4096"); // V8内存限制
        options.addArguments("--memory-pressure-off"); // 禁用内存压力检测
        options.addArguments("--max-unused-resource-memory-usage-percentage=5"); // 限制未使用资源内存
        options.addArguments("--aggressive-cache-discard"); // 积极清理缓存
        options.addArguments("--disable-background-timer-throttling"); // 禁用定时器节流
        options.addArguments("--disable-backgrounding-occluded-windows"); // 禁用被遮挡窗口后台处理
        options.addArguments("--disable-renderer-backgrounding"); // 禁用渲染器后台处理
    }

    /**
     * 渲染优化配置
     */
    private void addRenderingOptions(ChromeOptions options) {
        options.addArguments("--enable-accelerated-2d-canvas"); // 2D画布加速
        options.addArguments("--enable-accelerated-jpeg-decoding"); // JPEG解码加速
        options.addArguments("--enable-accelerated-mjpeg-decode"); // MJPEG解码加速
        options.addArguments("--font-render-hinting=none"); // 禁用字体提示
        options.addArguments("--disable-font-subpixel-positioning"); // 禁用字体子像素定位
    }

    /**
     * 网络和加载优化
     */
    private void addNetworkOptions(ChromeOptions options) {
        options.addArguments("--disable-background-networking"); // 禁用后台网络
        options.addArguments("--no-first-run"); // 跳过首次运行
        options.addArguments("--no-default-browser-check"); // 跳过默认浏览器检查
        options.addArguments("--disk-cache-size=0"); // 禁用磁盘缓存
        options.addArguments("--media-cache-size=0"); // 禁用媒体缓存
        options.addArguments("--disable-application-cache"); // 禁用应用缓存
    }

    /**
     * 禁用不必要功能
     */
    private void addDisableOptions(ChromeOptions options) {
        options.addArguments("--incognito"); // 无痕模式
        options.addArguments("--disable-plugins"); // 禁用插件
        options.addArguments("--disable-extensions"); // 禁用扩展
        options.addArguments("--disable-component-extensions-with-background-pages"); // 禁用后台扩展
        options.addArguments("--disable-notifications"); // 禁用通知
        options.addArguments("--disable-infobars"); // 禁用信息栏
        options.addArguments("--disable-translate"); // 禁用翻译
        options.addArguments("--disable-save-password-bubble"); // 禁用保存密码提示
        options.addArguments("--disable-client-side-phishing-detection"); // 禁用钓鱼检测
        options.addArguments("--disable-popup-blocking"); // 禁用弹窗阻止
        options.addArguments("--disable-default-apps"); // 禁用默认应用
        options.addArguments("--disable-sync"); // 禁用同步
        options.addArguments("--mute-audio"); // 静音
        options.addArguments("--disable-audio-output"); // 禁用音频输出
        options.addArguments("--disable-crash-reporter"); // 禁用崩溃报告
        options.addArguments("--disable-oopr-debug-crash-dump"); // 禁用调试崩溃转储
        options.addArguments("--no-crash-upload"); // 禁用崩溃上传
    }

    /**
     * 设置Chrome首选项
     */
    private void addPreferences(ChromeOptions options) {
        Map<String, Object> prefs = new HashMap<>();
        
        // 通知和弹窗设置
        prefs.put("profile.default_content_setting_values.notifications", 2);
        prefs.put("profile.default_content_settings.popups", 0);
        prefs.put("profile.managed_default_content_settings.images", 1);
        
        // 打印设置
        prefs.put("printing.print_preview_sticky_settings.appState", 
            "{\"recentDestinations\":[{\"id\":\"Save as PDF\",\"origin\":\"local\",\"account\":\"\"}]," +
            "\"selectedDestinationId\":\"Save as PDF\",\"version\":2}");
        
        // 下载设置
        prefs.put("savefile.default_directory", System.getProperty("java.io.tmpdir"));
        prefs.put("download.default_directory", System.getProperty("java.io.tmpdir"));
        prefs.put("download.prompt_for_download", false);
        prefs.put("download.directory_upgrade", true);
        
        // 安全设置
        prefs.put("safebrowsing.enabled", false);
        prefs.put("safebrowsing.disable_download_protection", true);
        
        options.setExperimentalOption("prefs", prefs);
        options.setExperimentalOption("useAutomationExtension", false);
        options.setExperimentalOption("excludeSwitches", new String[]{"enable-automation"});
        
        // 设置用户代理
        options.addArguments("--user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 " +
            "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 PDF-Generator/1.0");
    }

    /**
     * 配置日志选项
     */
    private void configureLogging(ChromeOptions options) {
        if (Boolean.TRUE.equals(chromeLogging)) {
            // 开发环境：启用详细日志
            options.addArguments("--verbose");
            options.addArguments("--log-level=0"); // INFO级别
            options.addArguments("--enable-logging");
            options.addArguments("--v=1"); // 详细级别
            
            LoggingPreferences logPrefs = new LoggingPreferences();
            logPrefs.enable(LogType.BROWSER, Level.INFO);
            logPrefs.enable(LogType.DRIVER, Level.INFO);
            logPrefs.enable(LogType.PERFORMANCE, Level.INFO);
            options.setCapability("goog:loggingPrefs", logPrefs);
            
            String logPath = System.getProperty("user.home") + "/chrome-pdf-generator.log";
            options.addArguments("--log-path=" + logPath);
        } else {
            // 生产环境：最小化日志
            options.addArguments("--log-level=3"); // 只记录致命错误
            options.addArguments("--silent");
            options.addArguments("--disable-logging");
        }
    }

    /**
     * 配置Headless模式
     */
    private void configureHeadlessMode(ChromeOptions options) {
        if (Boolean.TRUE.equals(chromeHeadless)) {
            options.addArguments("--headless=new"); // 新headless实现
            options.addArguments("--virtual-time-budget=" + (pageLoadTimeout * 1000)); // 虚拟时间预算
            options.addArguments("--disable-background-media-suspend"); // 禁用后台媒体暂停
            log.info("Chrome configured in headless mode with {}s timeout", pageLoadTimeout);
        } else {
            log.info("Chrome configured in GUI mode");
        }
    }

    /**
     * 设置ChromeDriver
     */
    private void setupChromeDriver(ChromeOptions options) {
        String version = options.getBrowserVersion();
        if (StringUtils.hasLength(version)) {
            Optional<Path> chromedriverPathOpt = ChromiumUtils.getChromedriverPath();
            if (chromedriverPathOpt.isPresent()) {
                log.info("Using chromedriver path: {}", chromedriverPathOpt.get());
                System.setProperty("webdriver.chrome.driver", chromedriverPathOpt.get().toString());
            } else {
                log.info("Downloading chromedriver version: {}", version);
                WebDriverManager.chromiumdriver().browserVersion(version).setup();
                log.info("Chromedriver version {} downloaded successfully", version);
            }
        } else {
            System.setProperty("webdriver.chrome.driver", "/usr/bin/chromedriver");
        }
        
        // 禁用driver日志输出
        System.setProperty("webdriver.chrome.silentOutput", "true");
    }

    // Getter和Setter方法
    public void setMainChromiumVersion(String mainChromiumVersion) {
        this.mainChromiumVersion = mainChromiumVersion;
    }

    public void setChromeHeadless(Boolean chromeHeadless) {
        this.chromeHeadless = chromeHeadless;
    }

    public void setChromeLogging(Boolean chromeLogging) {
        this.chromeLogging = chromeLogging;
    }
}
