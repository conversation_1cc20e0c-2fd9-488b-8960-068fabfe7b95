package io.nue.pdf.config;

import io.github.bonigarcia.wdm.WebDriverManager;
import io.nue.pdf.utils.ChromiumUtils;
import io.nue.pdf.utils.JsonUtils;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.logging.LogType;
import org.openqa.selenium.logging.LoggingPreferences;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import java.nio.file.Path;
import java.util.Map;
import java.util.Optional;
import java.util.logging.Level;

@Configuration
public class ChromeOptionsConfig {

    private static final Logger log = LoggerFactory.getLogger(ChromeOptionsConfig.class);
    @Value("${selenium.browser.main-version}")
    private String mainChromiumVersion;

    @Value("${selenium.browser.dev-version}")
    private String devChromiumVersion;
    @Value("${selenium.browser.headless:false}")
    private Boolean chromeHeadless;

    @Value("${selenium.browser.enable-logging:false}")
    private Boolean chromeLogging;
    
    @Bean("chromeOptions")
    public ChromeOptions chromiumOptions() {
        ChromeOptions options = baseOptions();

        Map<String, String> chromiumMap = ChromiumUtils.findChromiumBinary(mainChromiumVersion);
        log.info("chromiumOptions chromeBinary:{}", JsonUtils.serialize(chromiumMap));
        chromiumMap.entrySet().stream().forEach(entry -> {
            options.setBinary(entry.getKey());
            options.setBrowserVersion(entry.getValue());
        });

        String version = options.getBrowserVersion();
        if (StringUtils.hasLength(version)){
            Optional<Path> chromedriverPathOpt = ChromiumUtils.getChromedriverPath();
            if (chromedriverPathOpt.isPresent()){
                log.info("Using chromedriver path: {}", chromedriverPathOpt.get());
                System.setProperty("webdriver.chrome.driver", chromedriverPathOpt.get().toString());
            } else {
                log.info("Should download the Chromedriver version: {}", version);
                WebDriverManager.chromiumdriver().browserVersion(version).setup();
                log.info("Downloading chromedriver version: {} complete", version);
            }
        } else {
            System.setProperty("webdriver.chrome.driver", "/usr/bin/chromedriver");
        }

        log.info("chromeOptions options:{}", JsonUtils.serialize(options));
        return options;
    }

    private ChromeOptions baseOptions() {
        ChromeOptions options = new ChromeOptions();

        options.addArguments("--disable-gpu"); //GPU (Graphics Processing Unit) acceleration may lead to some compatibility issues or test failures
        options.addArguments("--no-sandbox"); //
        options.addArguments("--remote-allow-origins=*");
        options.addArguments("--enable-automation"); //

        options.addArguments("--window-size=1920,1080");
        options.addArguments("--canvas-2d-layers");
        options.addArguments("--enable-accelerated-2d-canvas");
        options.addArguments("--disable-dev-shm-usage"); // shared memory usage is disabled to prevent OOM errors
        options.addArguments("--start-maximized");
        options.addArguments("--w3c=true");

        options.addArguments("--force-color-profile=srgb"); // Ensure consistent color rendering
        options.addArguments("--disable-features=VizDisplayCompositor"); // Better CSS rendering
        options.addArguments("--run-all-compositor-stages-before-draw"); // Wait for all CSS to render

        options.addArguments("--disable-notifications"); // disable notifications
        options.addArguments("--disable-crash-reporter"); // disable extensions
        options.addArguments("--disable-infobars"); // disable infobars
        options.addArguments("--disable-renderer-backgrounding"); // disable extensions
        options.addArguments("--disable-background-timer-throttling");
        options.addArguments("--disable-backgrounding-occluded-windows");
        options.addArguments("--disable-client-side-phishing-detection");

        options.addArguments("--disable-notifications"); // disable notifications
        options.addArguments("--disable-crash-reporter"); // disable extensions
        options.addArguments("--disable-infobars"); // disable infobars
        options.addArguments("--disable-oopr-debug-crash-dump");
        options.addArguments("--no-crash-upload");
        options.addArguments("--disable-extensions");
        options.addArguments("--disable-low-res-tiling");
        options.addArguments("--silent");
        
        // chromium log options
        if (Boolean.TRUE.equals(chromeLogging)) {
            //enable chrome logging
            options.addArguments("--verbose");
            options.addArguments("--log-level=0"); // INFO = 0, WARNING = 1, LOG_ERROR = 2, LOG_FATAL = 3
            // Set logging preferences
            LoggingPreferences logPrefs = new LoggingPreferences();
            logPrefs.enable(LogType.BROWSER, Level.ALL);
            logPrefs.enable(LogType.DRIVER, Level.ALL);
            logPrefs.enable(LogType.PERFORMANCE, Level.ALL);
            options.setCapability("goog:loggingPrefs", logPrefs);
            // Enable Chrome's internal logging
            options.addArguments("--enable-logging");
            options.addArguments("--v=1");  // Verbosity level 1-3
            // Optional: Specify log output file (adjust path as needed)
            String userHome = System.getProperty("user.home");
            options.addArguments("--log-path=" + userHome + "/chrome.log");
        }

        options.addArguments("--incognito");
        // Headless mode
        if (Boolean.TRUE.equals(chromeHeadless)) {
            options.addArguments("--headless=new");// new implementation of headless mode    
        }

        return options;
    }

    public void setMainChromiumVersion(String mainChromiumVersion) {
        this.mainChromiumVersion = mainChromiumVersion;
    }

    public void setChromeHeadless(Boolean chromeHeadless) {
        this.chromeHeadless = chromeHeadless;
    }

    public void setChromeLogging(Boolean chromeLogging) {
        this.chromeLogging = chromeLogging;
    }
}
