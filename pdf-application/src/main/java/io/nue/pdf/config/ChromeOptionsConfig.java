package io.nue.pdf.config;

import io.github.bonigarcia.wdm.WebDriverManager;
import io.nue.pdf.utils.ChromiumUtils;
import io.nue.pdf.utils.JsonUtils;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.logging.LogType;
import org.openqa.selenium.logging.LoggingPreferences;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.logging.Level;

@Configuration
public class ChromeOptionsConfig {

    private static final Logger log = LoggerFactory.getLogger(ChromeOptionsConfig.class);
    @Value("${selenium.browser.main-version}")
    private String mainChromiumVersion;

    @Value("${selenium.browser.dev-version}")
    private String devChromiumVersion;
    @Value("${selenium.browser.headless:false}")
    private Boolean chromeHeadless;

    @Value("${selenium.browser.enable-logging:false}")
    private Boolean chromeLogging;
    
    @Bean("chromeOptions")
    public ChromeOptions chromiumOptions() {
        ChromeOptions options = baseOptions();

        Map<String, String> chromiumMap = ChromiumUtils.findChromiumBinary(mainChromiumVersion);
        log.info("chromiumOptions chromeBinary:{}", JsonUtils.serialize(chromiumMap));
        chromiumMap.entrySet().stream().forEach(entry -> {
            options.setBinary(entry.getKey());
            options.setBrowserVersion(entry.getValue());
        });

        String version = options.getBrowserVersion();
        if (StringUtils.hasLength(version)){
            Optional<Path> chromedriverPathOpt = ChromiumUtils.getChromedriverPath();
            if (chromedriverPathOpt.isPresent()){
                log.info("Using chromedriver path: {}", chromedriverPathOpt.get());
                System.setProperty("webdriver.chrome.driver", chromedriverPathOpt.get().toString());
            } else {
                log.info("Should download the Chromedriver version: {}", version);
                WebDriverManager.chromiumdriver().browserVersion(version).setup();
                log.info("Downloading chromedriver version: {} complete", version);
            }
        } else {
            System.setProperty("webdriver.chrome.driver", "/usr/bin/chromedriver");
        }

        log.info("chromeOptions options:{}", JsonUtils.serialize(options));
        return options;
    }

    private ChromeOptions baseOptions() {
        ChromeOptions options = new ChromeOptions();

        // ========== 核心安全和稳定性配置 ==========
        options.addArguments("--no-sandbox"); // 在容器环境中必需
        options.addArguments("--disable-dev-shm-usage"); // 防止共享内存不足导致的崩溃
        options.addArguments("--disable-gpu"); // 避免GPU相关的兼容性问题
        options.addArguments("--remote-allow-origins=*"); // 允许远程连接
        options.addArguments("--disable-web-security"); // 禁用同源策略限制
        options.addArguments("--disable-features=VizDisplayCompositor"); // 提高渲染稳定性

        // ========== PDF打印优化配置 ==========
        options.addArguments("--enable-print-preview"); // 启用打印预览功能
        options.addArguments("--kiosk-printing"); // 静默打印模式
        options.addArguments("--run-all-compositor-stages-before-draw"); // 确保所有渲染完成
        options.addArguments("--disable-print-preview"); // 禁用打印预览对话框

        // ========== 窗口和显示配置 ==========
        options.addArguments("--window-size=1920,1080"); // 设置窗口大小
        options.addArguments("--start-maximized"); // 最大化窗口
        options.addArguments("--force-device-scale-factor=1"); // 固定缩放比例
        options.addArguments("--high-dpi-support=1"); // 高DPI支持
        options.addArguments("--force-color-profile=srgb"); // 确保颜色一致性

        // ========== 性能优化配置 ==========
        options.addArguments("--max_old_space_size=4096"); // 增加V8内存限制
        options.addArguments("--memory-pressure-off"); // 禁用内存压力检测
        options.addArguments("--max-unused-resource-memory-usage-percentage=5"); // 限制未使用资源内存
        options.addArguments("--aggressive-cache-discard"); // 积极清理缓存
        options.addArguments("--enable-precise-memory-info"); // 启用精确内存信息

        // ========== 渲染优化配置 ==========
        options.addArguments("--disable-background-timer-throttling"); // 禁用后台定时器节流
        options.addArguments("--disable-backgrounding-occluded-windows"); // 禁用被遮挡窗口的后台处理
        options.addArguments("--disable-renderer-backgrounding"); // 禁用渲染器后台处理
        options.addArguments("--disable-features=TranslateUI"); // 禁用翻译UI
        options.addArguments("--disable-features=BlinkGenPropertyTrees"); // 禁用某些实验性功能
        options.addArguments("--enable-accelerated-2d-canvas"); // 启用2D画布加速
        options.addArguments("--enable-accelerated-jpeg-decoding"); // 启用JPEG解码加速
        options.addArguments("--enable-accelerated-mjpeg-decode"); // 启用MJPEG解码加速

        // ========== 网络和加载优化 ==========
        options.addArguments("--aggressive-cache-discard"); // 积极清理缓存
        options.addArguments("--disable-background-networking"); // 禁用后台网络
        options.addArguments("--disable-default-apps"); // 禁用默认应用
        options.addArguments("--disable-sync"); // 禁用同步
        options.addArguments("--no-first-run"); // 跳过首次运行设置
        options.addArguments("--no-default-browser-check"); // 跳过默认浏览器检查

        // ========== 安全和隐私配置 ==========
        options.addArguments("--incognito"); // 无痕模式
        options.addArguments("--disable-plugins"); // 禁用插件
        options.addArguments("--disable-extensions"); // 禁用扩展
        options.addArguments("--disable-component-extensions-with-background-pages"); // 禁用后台扩展
        options.addArguments("--disable-client-side-phishing-detection"); // 禁用钓鱼检测
        options.addArguments("--disable-popup-blocking"); // 禁用弹窗阻止

        // ========== 通知和UI禁用 ==========
        options.addArguments("--disable-notifications"); // 禁用通知
        options.addArguments("--disable-infobars"); // 禁用信息栏
        options.addArguments("--disable-translate"); // 禁用翻译
        options.addArguments("--disable-save-password-bubble"); // 禁用保存密码提示

        // ========== 崩溃报告和调试禁用 ==========
        options.addArguments("--disable-crash-reporter"); // 禁用崩溃报告
        options.addArguments("--disable-oopr-debug-crash-dump"); // 禁用调试崩溃转储
        options.addArguments("--no-crash-upload"); // 禁用崩溃上传
        options.addArguments("--silent"); // 静默模式

        // ========== 自动化和测试配置 ==========
        options.addArguments("--enable-automation"); // 启用自动化标识
        options.addArguments("--disable-blink-features=AutomationControlled"); // 隐藏自动化控制标识

        // ========== 实验性功能和优化 ==========
        options.addArguments("--enable-features=NetworkService,NetworkServiceLogging"); // 启用网络服务
        options.addArguments("--disable-features=VizDisplayCompositor,AudioServiceOutOfProcess"); // 禁用可能影响稳定性的功能
        options.addArguments("--disable-ipc-flooding-protection"); // 禁用IPC洪水保护
        options.addArguments("--disable-hang-monitor"); // 禁用挂起监控
        options.addArguments("--disable-prompt-on-repost"); // 禁用重新提交提示

        // ========== 媒体和音频禁用 ==========
        options.addArguments("--mute-audio"); // 静音
        options.addArguments("--disable-audio-output"); // 禁用音频输出
        options.addArguments("--autoplay-policy=no-user-gesture-required"); // 允许自动播放

        // ========== 字体和文本渲染优化 ==========
        options.addArguments("--font-render-hinting=none"); // 禁用字体提示
        options.addArguments("--disable-font-subpixel-positioning"); // 禁用字体子像素定位
        options.addArguments("--disable-lcd-text"); // 禁用LCD文本渲染

        // ========== 页面加载和缓存策略 ==========
        options.addArguments("--disk-cache-size=0"); // 禁用磁盘缓存
        options.addArguments("--media-cache-size=0"); // 禁用媒体缓存
        options.addArguments("--disable-application-cache"); // 禁用应用缓存
        options.addArguments("--disable-offline-load-stale-cache"); // 禁用离线缓存

        // ========== 设置用户代理和平台标识 ==========
        options.addArguments("--user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 PDF-Generator/1.0");

        // ========== 设置首选项 ==========
        Map<String, Object> prefs = new HashMap<>();
        prefs.put("profile.default_content_setting_values.notifications", 2); // 禁用通知
        prefs.put("profile.default_content_settings.popups", 0); // 禁用弹窗
        prefs.put("profile.managed_default_content_settings.images", 1); // 允许图片
        prefs.put("profile.content_settings.plugin_whitelist.adobe-flash-player", 1); // Flash设置
        prefs.put("profile.content_settings.exceptions.plugins.*,*.per_resource.adobe-flash-player", 1);
        prefs.put("profile.default_content_setting_values.plugins", 1);
        prefs.put("profile.content_settings.plugin_whitelist.adobe-flash-player", 1);
        prefs.put("profile.content_settings.exceptions.plugins.*,*.per_resource.adobe-flash-player", 1);
        prefs.put("printing.print_preview_sticky_settings.appState", "{\"recentDestinations\":[{\"id\":\"Save as PDF\",\"origin\":\"local\",\"account\":\"\"}],\"selectedDestinationId\":\"Save as PDF\",\"version\":2}");
        prefs.put("savefile.default_directory", System.getProperty("java.io.tmpdir")); // 设置默认下载目录
        options.setExperimentalOption("prefs", prefs);

        // ========== 性能监控和调试选项 ==========
        options.setExperimentalOption("useAutomationExtension", false); // 禁用自动化扩展
        options.setExperimentalOption("excludeSwitches", new String[]{"enable-automation"}); // 排除自动化开关

        // ========== 日志配置 ==========
        if (Boolean.TRUE.equals(chromeLogging)) {
            // 启用详细日志
            options.addArguments("--verbose");
            options.addArguments("--log-level=0"); // INFO = 0, WARNING = 1, LOG_ERROR = 2, LOG_FATAL = 3
            options.addArguments("--enable-logging");
            options.addArguments("--v=1"); // 详细级别 1-3

            // 设置日志首选项
            LoggingPreferences logPrefs = new LoggingPreferences();
            logPrefs.enable(LogType.BROWSER, Level.INFO);
            logPrefs.enable(LogType.DRIVER, Level.INFO);
            logPrefs.enable(LogType.PERFORMANCE, Level.INFO);
            logPrefs.enable(LogType.CLIENT, Level.INFO);
            options.setCapability("goog:loggingPrefs", logPrefs);

            // 指定日志输出文件
            String userHome = System.getProperty("user.home");
            options.addArguments("--log-path=" + userHome + "/chrome-pdf-generator.log");
            options.addArguments("--enable-chrome-browser-cloud-management"); // 启用浏览器云管理日志
        } else {
            // 生产环境禁用所有日志
            options.addArguments("--log-level=3"); // 只记录致命错误
            options.addArguments("--silent");
            options.addArguments("--disable-logging");
        }

        // ========== Headless模式配置 ==========
        if (Boolean.TRUE.equals(chromeHeadless)) {
            options.addArguments("--headless=new"); // 使用新的headless实现
            options.addArguments("--virtual-time-budget=30000"); // 设置虚拟时间预算(30秒)
            options.addArguments("--disable-background-media-suspend"); // 禁用后台媒体暂停
        }

        return options;
    }

    public void setMainChromiumVersion(String mainChromiumVersion) {
        this.mainChromiumVersion = mainChromiumVersion;
    }

    public void setChromeHeadless(Boolean chromeHeadless) {
        this.chromeHeadless = chromeHeadless;
    }

    public void setChromeLogging(Boolean chromeLogging) {
        this.chromeLogging = chromeLogging;
    }
}
