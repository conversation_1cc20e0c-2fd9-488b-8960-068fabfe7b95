package io.nue.pdf.common;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_EMPTY;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(NON_EMPTY)
public class ErrorMessage {

    private final String title;
    private final String message;
    private final String errorCode;

    public ErrorMessage(String title, String message, String errorCode) {
        this.title = title;
        this.message = message;
        this.errorCode = errorCode;
    }

    public String getTitle() {
        return title;
    }

    public String getMessage() {
        return message;
    }

    public String getErrorCode() {
        return errorCode;
    }
}
