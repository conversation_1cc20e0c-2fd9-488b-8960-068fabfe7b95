package io.nue.pdf.utils;

import static com.fasterxml.jackson.core.JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.util.StdDateFormat;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;

public class JsonUtils {

    private static final Logger logger = LoggerFactory.getLogger(JsonUtils.class);

    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.registerModules(new ParameterNamesModule(), new Jdk8Module(), new JavaTimeModule());
        objectMapper.enable(WRITE_BIGDECIMAL_AS_PLAIN);
        objectMapper.setVisibility(PropertyAccessor.FIELD, JsonAutoDetect.Visibility.ANY);
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapper.setDateFormat(new StdDateFormat());
    }

    public static String serialize(Object object){
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            logger.error("jsonException",e);
            throw new RuntimeException("NUE_COMMON_JSON_SERIALIZE_ERROR", e);
        }
    }

    public static <T> T deserialize(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            logger.error("jsonException",e);
            throw new RuntimeException("NUE_COMMON_JSON_DESERIALIZE_ERROR",  e);
        }
    }
    
    public static <T> T deserialize(String json, TypeReference<T> typeReference){
        try {
            return objectMapper.readValue(json, typeReference);
        } catch (IOException e) {
            logger.error("jsonException",e);
            throw new RuntimeException("NUE_COMMON_JSON_DESERIALIZE_ERROR", e);
        }
    }

    /**
     *
     * @param in
     * @param typeReference
     * @param <T>
     * @return
     */
    public static <T> T deserialize(InputStream in, TypeReference<T> typeReference){
        try {
            return objectMapper.readValue(in, typeReference);
        } catch (IOException e) {
            logger.error("jsonException",e);
            throw new RuntimeException("NUE_COMMON_JSON_DESERIALIZE_ERROR", e);
        }
    }

}
