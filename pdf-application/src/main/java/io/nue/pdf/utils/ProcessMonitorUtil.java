package io.nue.pdf.utils;

import oshi.SystemInfo;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.software.os.OSProcess;
import oshi.software.os.OperatingSystem;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ProcessMonitorUtil {

    private static final SystemInfo systemInfo = new SystemInfo();
    private static final OperatingSystem os = systemInfo.getOperatingSystem();
    private static final HardwareAbstractionLayer hardware = systemInfo.getHardware();

    /**
     * Gets top N processes by CPU usage
     */
    public static List<Map<String, Object>> getTopCpuProcesses(int topN) {
        return os.getProcesses().stream()
                .sorted((p1, p2) -> Double.compare(p2.getProcessCpuLoadCumulative(), p1.getProcessCpuLoadCumulative()))
                .limit(topN)
                .map(ProcessMonitorUtil::processToMap)
                .collect(Collectors.toList());
    }

    /**
     * Gets top N processes by memory usage
     */
    public static List<Map<String, Object>> getTopMemoryProcesses(int topN) {
        return os.getProcesses().stream()
                .sorted((p1, p2) -> Long.compare(p2.getResidentSetSize(), p1.getResidentSetSize()))
                .limit(topN)
                .map(ProcessMonitorUtil::processToMap)
                .collect(Collectors.toList());
    }

    /**
     * Gets all processes with specified name pattern
     */
    public static List<Map<String, Object>> getProcessesByName(String name) {
        return os.getProcesses().stream()
                .filter(p -> p.getName().toLowerCase().contains(name.toLowerCase()))
                .map(ProcessMonitorUtil::processToMap)
                .collect(Collectors.toList());
    }

    /**
     * Gets all Chrome-related processes
     */
    public static List<Map<String, Object>> getChromeProcesses() {
        return os.getProcesses().stream()
                .filter(p -> p.getName().toLowerCase().contains("chrome"))
                .map(ProcessMonitorUtil::processToMap)
                .collect(Collectors.toList());
    }

    /**
     * Converts process information to Map
     */
    private static Map<String, Object> processToMap(OSProcess process) {
        double cpuUsage = process.getProcessCpuLoadCumulative() * 100; // Cumulative CPU usage
        long memoryBytes = process.getResidentSetSize();
        double memoryMB = memoryBytes / (1024.0 * 1024.0);

        return Map.of(
                "pid", process.getProcessID(),
                "name", process.getName(),
                "commandLine", String.join(" ", process.getCommandLine()),
                "cpuUsage", String.format("%.2f%%", cpuUsage),
                "memoryUsageMB", String.format("%.2f", memoryMB),
                "threadCount", process.getThreadCount(),
                "priority", process.getPriority(),
                "state", process.getState().name(),
                "startTime", process.getStartTime(),
                "upTime", process.getUpTime()
        );
    }

    /**
     * Gets overall system resource usage
     */
    public static Map<String, Object> getSystemResourceUsage() {
        double cpuLoad = hardware.getProcessor().getSystemCpuLoad(0) * 100;
        long totalMemory = hardware.getMemory().getTotal();
        long availableMemory = hardware.getMemory().getAvailable();

        return Map.of(
                "systemCpuUsage", String.format("%.2f%%", cpuLoad),
                "totalMemoryGB", String.format("%.2f", totalMemory / (1024.0 * 1024.0 * 1024.0)),
                "usedMemoryGB", String.format("%.2f", (totalMemory - availableMemory) / (1024.0 * 1024.0 * 1024.0)),
                "memoryUsagePercent", String.format("%.2f%%", (totalMemory - availableMemory) * 100.0 / totalMemory),
                "processCount", os.getProcessCount(),
                "threadCount", os.getThreadCount()
        );
    }

}