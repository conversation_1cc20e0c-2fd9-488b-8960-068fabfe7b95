package io.nue.pdf.utils;

import com.google.common.collect.Lists;
import org.openqa.selenium.TimeoutException;
import org.openqa.selenium.WebDriverException;
import org.openqa.selenium.internal.Require;
import org.openqa.selenium.support.ui.Sleeper;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;

public class FileWait<T> {

    protected static final long DEFAULT_SLEEP_TIMEOUT = 500L;
    private static final Duration DEFAULT_WAIT_DURATION = Duration.ofMillis(500L);
    private final T input;
    private final Clock clock;
    private final Sleeper sleeper;
    private Duration timeout;
    private Duration interval;
    private Supplier<String> messageSupplier;
    private final List<Class<? extends Throwable>> ignoredExceptions;

    public FileWait(T input) {
        this(input, Clock.systemDefaultZone(), Sleeper.SYSTEM_SLEEPER);
    }

    public FileWait(T input, Clock clock, Sleeper sleeper) {
        this.timeout = DEFAULT_WAIT_DURATION;
        this.interval = DEFAULT_WAIT_DURATION;
        this.messageSupplier = () -> {
            return null;
        };
        this.ignoredExceptions = new ArrayList();
        this.input = input;
        this.clock = (Clock) Require.nonNull("Clock", clock);
        this.sleeper = (Sleeper) Require.nonNull("Sleeper", sleeper);
    }

    public FileWait<T> withTimeout(Duration timeout) {
        this.timeout = timeout;
        return this;
    }

    public FileWait<T> withMessage(final String message) {
        this.messageSupplier = () -> {
            return message;
        };
        return this;
    }

    public FileWait<T> withMessage(Supplier<String> messageSupplier) {
        this.messageSupplier = messageSupplier;
        return this;
    }

    public FileWait<T> pollingEvery(Duration interval) {
        this.interval = interval;
        return this;
    }

    public <K extends Throwable> FileWait<T> ignoreAll(Collection<Class<? extends K>> types) {
        this.ignoredExceptions.addAll(types);
        return this;
    }

    public FileWait<T> ignoring(Class<? extends Throwable> exceptionType) {
        return this.ignoreAll(Lists.newArrayList(exceptionType));
    }

    public FileWait<T> ignoring(Class<? extends Throwable> firstType, Class<? extends Throwable> secondType) {
        return this.ignoreAll(Lists.newArrayList(firstType, secondType));
    }

    public <V> V until(Function<? super T, V> isTrue) {
        Instant end = this.clock.instant().plus(this.timeout);
        
        while (true) {
            Throwable lastException;
            try {
                V value = isTrue.apply(this.input);
                if (value != null && (Boolean.class != value.getClass() || Boolean.TRUE.equals(value))) {
                    return value;
                }
                lastException = null;
            } catch (Throwable var7) {
                lastException = this.propagateIfNotIgnored(var7);
            }

            if (end.isBefore(this.clock.instant())) {
                String message = this.messageSupplier != null ? (String) this.messageSupplier.get() : null;
                String timeoutMessage = String.format("Expected condition failed: %s (tried for %d second(s) with %d milliseconds interval)",
                        message == null ? "waiting for " + String.valueOf(isTrue) : message, this.timeout.getSeconds(), this.interval.toMillis());
                throw this.timeoutException(timeoutMessage, lastException);
            }

            try {
                this.sleeper.sleep(this.interval);
            } catch (InterruptedException var6) {
                Thread.currentThread().interrupt();
                throw new WebDriverException(var6);
            }
        }
    }

    private Throwable propagateIfNotIgnored(Throwable e) {
        Iterator var2 = this.ignoredExceptions.iterator();

        Class ignoredException;
        do {
            if (!var2.hasNext()) {
                if (e instanceof Error) {
                    throw (Error) e;
                }

                if (e instanceof RuntimeException) {
                    throw (RuntimeException) e;
                }

                throw new RuntimeException(e);
            }

            ignoredException = (Class) var2.next();
        } while (!ignoredException.isInstance(e));

        return e;
    }

    protected RuntimeException timeoutException(String message, Throwable lastException) {
        throw new TimeoutException(message, lastException);
    }
}
