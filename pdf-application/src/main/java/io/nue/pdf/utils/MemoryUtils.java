package io.nue.pdf.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.management.ManagementFactory;
import com.sun.management.OperatingSystemMXBean;

public class MemoryUtils {

    private static final Logger log = LoggerFactory.getLogger(MemoryUtils.class);
    public static void printMemoryUsage() {
        OperatingSystemMXBean osBean = ManagementFactory.getPlatformMXBean(OperatingSystemMXBean.class);

        // Total physical memory
        long totalMemory = osBean.getTotalMemorySize();
        // Free physical memory
        long freeMemory = osBean.getFreeMemorySize();
        
        log.info("Memory Usage Information:");
        log.info("Total Memory: {}", formatSize(totalMemory));
        log.info("Free Memory: {}", formatSize(freeMemory));
        log.info("Used Memory: {}", formatSize(totalMemory - freeMemory));
        log.info("Percent Used: {}%", String.format("%.2f", ((totalMemory - freeMemory) * 100.0) / totalMemory));
    }

    public static void printJVMMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();

        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        long usedMemory = totalMemory - freeMemory;

        log.info("JVM Memory Usage Information:");
        log.info("JVM Total Memory: {}", formatSize(totalMemory));
        log.info("JVM Free Memory: {}", formatSize(freeMemory));
        log.info("JVM Used Memory: {}", formatSize(usedMemory));
        log.info("JVM Max Memory: {}", formatSize(maxMemory));
        log.info("Percent Used: {}%", String.format("%.2f", (usedMemory * 100.0) / maxMemory));
    }
    
    private static String formatSize(long bytes) {
        final long kilobyte = 1024;
        final long megabyte = kilobyte * 1024;
        final long gigabyte = megabyte * 1024;

        if (bytes >= gigabyte) {
            return String.format("%.2f GB", (double) bytes / gigabyte);
        } else if (bytes >= megabyte) {
            return String.format("%.2f MB", (double) bytes / megabyte);
        } else if (bytes >= kilobyte) {
            return String.format("%.2f KB", (double) bytes / kilobyte);
        } else {
            return bytes + " bytes";
        }
    }

}
