package io.nue.pdf.utils;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.ThreadInfo;
import java.lang.management.ThreadMXBean;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class JvmThreadMonitorUtil {

    private static final ThreadMXBean threadMXBean = ManagementFactory.getThreadMXBean();
    private static final MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();

    /**
     * getTopThreadCpu
     */
    public static List<Map<String, Object>> getTopThreadCpu(int topN) {
        long[] ids = threadMXBean.getAllThreadIds();
        List<Map<String, Object>> threadStats = Arrays.stream(ids)
                .mapToObj(id -> {

                    ThreadInfo info = threadMXBean.getThreadInfo(id);
                    if (info != null) {
                        long cpuTime = threadMXBean.getThreadCpuTime(id);
                        double cpuUsage = cpuTime / 1_000_000.0; // to miliseconds

                        Map<String, Object> threadStat = new HashMap<>();
                        threadStat.put("id", id);
                        threadStat.put("name", info.getThreadName());
                        threadStat.put("state", info.getThreadState().name());
                        threadStat.put("cpuTimeMs", cpuUsage);
                        threadStat.put("blockedCount", info.getBlockedCount());
                        threadStat.put("blockedTime", info.getBlockedTime());
                        threadStat.put("waitedCount", info.getWaitedCount());
                        threadStat.put("waitedTime", info.getWaitedTime());
                        threadStat.put("lockName", Optional.ofNullable(info.getLockName()).orElse("none"));
                        threadStat.put("lockOwnerName", Optional.ofNullable(info.getLockOwnerName()).orElse("none"));

                        return threadStat;
                    }

                    return null;
                })
                .filter(Objects::nonNull)
                .sorted((a, b) -> Double.compare((Double) b.get("cpuTimeMs"), (Double) a.get("cpuTimeMs")))
                .limit(topN)
                .collect(Collectors.toList());

        return threadStats;
    }

    /**
     * getThreadGroupStats
     */
    public static Map<String, Object> getThreadGroupStats() {
        ThreadInfo[] threadInfos = threadMXBean.dumpAllThreads(false, false);

        Map<Thread.State, Long> stateCount = Arrays.stream(threadInfos)
                .collect(Collectors.groupingBy(
                        ThreadInfo::getThreadState,
                        Collectors.counting()
                ));

        return Map.of(
                "totalThreadCount", threadMXBean.getThreadCount(),
                "daemonThreadCount", threadMXBean.getDaemonThreadCount(),
                "peakThreadCount", threadMXBean.getPeakThreadCount(),
                "totalStartedThreadCount", threadMXBean.getTotalStartedThreadCount(),
                "threadStateCount", stateCount
        );
    }

    /**
     * getDeadlockedThreads
     */
    public static List<Map<String, Object>> getDeadlockedThreads() {
        long[] deadlockedIds = threadMXBean.findDeadlockedThreads();
        if (deadlockedIds == null) {
            return Collections.emptyList();
        }

        return Arrays.stream(deadlockedIds)
                .mapToObj(id -> {
                    ThreadInfo[] infoList = threadMXBean.getThreadInfo(new long[]{id}, true, true);
                    if (infoList != null && infoList.length > 0) {
                        ThreadInfo info = infoList[0];

                        Map<String, Object> threadStat = new HashMap<>();
                        threadStat.put("id", id);
                        threadStat.put("name", info.getThreadName());
                        threadStat.put("state", info.getThreadState().name());
                        threadStat.put("lockName", Optional.ofNullable(info.getLockName()).orElse("none"));
                        threadStat.put("lockOwnerName", Optional.ofNullable(info.getLockOwnerName()).orElse("none"));
                        threadStat.put("lockOwnerId", info.getLockOwnerId());
                        threadStat.put("stackTrace", Arrays.toString(info.getStackTrace()));

                        return threadStat;
                    }

                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

}