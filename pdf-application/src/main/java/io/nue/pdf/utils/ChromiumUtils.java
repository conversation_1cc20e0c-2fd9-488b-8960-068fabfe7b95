package io.nue.pdf.utils;

import com.google.common.collect.ImmutableMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.FileSystems;
import java.nio.file.FileVisitResult;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.PathMatcher;
import java.nio.file.Paths;
import java.nio.file.SimpleFileVisitor;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ChromiumUtils {

    private static final Logger log = LoggerFactory.getLogger(ChromiumUtils.class);
    private static final String CHROMIUM_MACOS_PATH = "glob:**/Chromium.app/Contents/MacOS/Chromium";
    private static final String CHROMIUM_LINUX_PATH = "glob:**/chromium";
    private static final String CHROMIUM_DOWNLOADER_SCRIPT = "chromium-dev-init.sh";
    private static final String PROJECT_HOME = System.getProperty("user.dir");

    /**
     * key:chromium path ,value : chromium version
     * @param chromiumMainVersion
     * @return
     */
    public static Map<String, String> findChromiumBinary(String chromiumMainVersion) {
        // Not dev environment,run command: which chromium  
        // chromium have already installed in the docker image.
        Optional<String> chromiumPathOpt = chromiumInSystem();
        Path chromiumExeDir = null;
        if (chromiumPathOpt.isPresent()) { //
            chromiumExeDir = Paths.get(chromiumPathOpt.get());
            log.info("Chromium installed in system, at: " + chromiumExeDir);
            String chromiumVersion = isSystemChromiumVersionCorrect(chromiumMainVersion, null);
            if (StringUtils.hasLength(chromiumVersion)) {
                log.info("If it is Correct version, chromiumVersion: {}", chromiumVersion);
                return new HashMap<>();
            }
        }

        // project home
        Optional<Path> projectHomeOpt = findProjectHome();
        if (projectHomeOpt.isPresent()){
            // installed chromium in macOS
            Optional<Path> chromiumPath = chromiumInProject(projectHomeOpt.get().toString());
            if (!chromiumPath.isPresent()) {
                callChromiumScript(projectHomeOpt.get().toString());
                chromiumPath = chromiumInProject(projectHomeOpt.get().toString());
            }
            chromiumExeDir = chromiumPath.get();
            
            String chromiumVersion = isSystemChromiumVersionCorrect(chromiumMainVersion, chromiumExeDir.getParent().toString());
            
            return ImmutableMap.of(chromiumExeDir.toString(), chromiumVersion);
        }
        
        return null;
    }

    private static Optional<Path> findProjectHome() {
        Path projectHome = Paths.get(PROJECT_HOME);
        Path scriptPath = projectHome.resolve(CHROMIUM_DOWNLOADER_SCRIPT);

        if (Files.exists(scriptPath)) {
            return Optional.of(projectHome);
        }

        // If not found in PROJECT_HOME, search in the parent directory
        Path parentDir = projectHome.getParent();
        if (parentDir != null) {
            scriptPath = parentDir.resolve(CHROMIUM_DOWNLOADER_SCRIPT);
            if (Files.exists(scriptPath)) {
                return Optional.of(parentDir);
            }
        }

        // If still not found, return null or throw an exception
        return Optional.empty();
    }

    public static Optional<Path> getChromedriverPath() {
        Optional<Path> projectHomeOpt = findProjectHome();
        if (projectHomeOpt.isPresent()) {
            
            Path chromedriverPath = Paths.get(projectHomeOpt.get().toString(), "chromium/chromedriver");

            if (Files.exists(chromedriverPath)) {
                return Optional.of(chromedriverPath);
            }
        }

        return Optional.empty();
    }
    
    private static Optional<Path> chromiumInProject(String projectHome) {
        Path chromiumExeDir = Paths.get(projectHome, "chromium");
        log.info("Searching for chromium executable in project home {}", chromiumExeDir);
        // only support macos and linux
        PathMatcher matcher = FileSystems.getDefault().getPathMatcher(CHROMIUM_MACOS_PATH);

        AtomicReference<Path> result = new AtomicReference<>();
        try {
            Files.walkFileTree(chromiumExeDir, new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) {
                    if (matcher.matches(file)) {
                        result.set(file);
                        return FileVisitResult.TERMINATE;
                    }
                    return FileVisitResult.CONTINUE;
                }
            });
        } catch (IOException e) {
            log.error("Error while searching for chromium executable", e);
            return Optional.empty();
        }

        return Optional.ofNullable(result.get());
    }
    
    private static String isSystemChromiumVersionCorrect(String chromiumVersion,String chromiumExeDir) {
        ProcessBuilder processBuilder = null;
        log.info("Checking system chromium version, chromiumExeDir:{}",chromiumExeDir);
        if (StringUtils.hasLength(chromiumExeDir)) {
            processBuilder = new ProcessBuilder("./Chromium", "--version");
            processBuilder.directory(new File(chromiumExeDir));
        }else {
            processBuilder = new ProcessBuilder("chromium", "--version");
        }
        log.info("Checking Chromium --version , in path: {}", chromiumVersion);
        try {
            Process process = processBuilder.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String versionInfo = reader.readLine();
            log.info("Chromium version info: {}<<<", versionInfo);
            
            if (versionInfo != null && versionInfo.contains(chromiumVersion)) {
                if (StringUtils.hasLength(chromiumExeDir)) {
                    Pattern versionPattern = Pattern.compile("Chromium\s(\\d+?\\.\\d+?\\.\\d+?\\.\\d+?)\s");
                    Matcher matcher = versionPattern.matcher(versionInfo);
                    if (matcher.find()) {
                        versionInfo = matcher.group(1);
                    }
                }
                log.info("chromium versionInfo: {}<<<", versionInfo);
                return versionInfo;
            }

            int exitCode = process.waitFor();
            if (exitCode != 0) {
                log.error("Error: Chromium process exited with code {}", exitCode);
            }
        } catch (Exception e) {
            log.error("Error checking Chromium version: {}", e.getMessage());
        }

        return null;
    }
    
    public static boolean callChromiumScript(String projectHome) {
        log.info("/bin/bash {}", CHROMIUM_DOWNLOADER_SCRIPT);
        ProcessBuilder processBuilder = new ProcessBuilder("/bin/bash", "chromium-dev-init.sh");
        processBuilder.directory(new File(projectHome));
        try {
            processBuilder.redirectErrorStream(true);
            Process process = processBuilder.start();

            // Capture and print the script's output
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line = null;
            boolean isFirstLine = true;
            while ((line = reader.readLine()) != null) {
                if (isFirstLine) {
                    isFirstLine = false;
                    log.info("first time output");
                }
                log.info(line);
            }

            // Capture and print any error output
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            while ((line = errorReader.readLine()) != null) {
                log.error(line);
                line = null;
            }

            int exitCode = process.waitFor();
            if (exitCode == 0) {
                log.error("Chromium process successfully exited with code " + exitCode);
                return true;
            }
        } catch (Exception e) {
            log.error("Call chromium script error", e);
        }

        return false;
    }

    public static Optional<String> chromiumInSystem() {
        try {
            ProcessBuilder processBuilder = new ProcessBuilder("which", "chromium");
            processBuilder.redirectErrorStream(true);
            Process process = processBuilder.start();

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line = reader.readLine();

            int exitCode = process.waitFor();

            if (exitCode == 0 && line != null && !line.isEmpty()) {
                log.info("Chromium found at: " + line);
                return Optional.of(line);
            } else {
                log.error("Chromium not installed in system.");
                return Optional.empty();
            }
        } catch (Exception e) {
            log.error("Error checking for Chromium: " + e.getMessage());
            return Optional.empty();
        }
    }

}
