package io.nue.pdf.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.FileSystems;
import java.nio.file.FileVisitResult;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.PathMatcher;
import java.nio.file.Paths;
import java.nio.file.SimpleFileVisitor;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

public class ChromeExecutationUtils {

    private static final Logger log = LoggerFactory.getLogger(ChromeExecutationUtils.class);
    private static final String CHROME_MACOS_PATH = "glob:**/Google Chrome for Testing.app/Contents/MacOS/Google Chrome for Testing";
    private static final String CHROME_LINUX_PATH = "glob:**/chrome";
    private static final String CHROME_VERSION_PREFIX = "Google Chrome for Testing ";
    private static final String CHROME_LINUX_VERSION_PREFIX = "Google Chrome ";
    private static final String CHROME_DOWNLOADER_SCRIPT = "chromium-init.sh";
    private static final String PROJECT_HOME = System.getProperty("user.dir");

    public static String findChrome(String chromeVersion) {
        log.info("Searching for chrome executable in project home");
        Optional<Path> chromeBinaryOptional = findChromeExecutable();
        // find
        if (chromeBinaryOptional.isPresent()) {
            String chromeBinary = chromeBinaryOptional.get().toString();
            log.info("If it is Correct version, chromeVersion: {}", chromeVersion);
            boolean isChromeVersion = isRightChromeVersion(Paths.get(chromeBinary), chromeVersion);
            // version not match
            if (!isChromeVersion) {
                if (!callChromeScript()) {
                    throw new RuntimeException("Chrome executable not found or OS not support!");
                }
            }
            log.info("chromeBinary:{}", chromeBinary);
            return chromeBinary;
        } else {//not find, only happen in dev environment.
            log.info("Chrome executable not found in project home, using default chrome driver");
            if (!callChromeScript()) {
                throw new RuntimeException("Chrome executable not found or OS not support!");
            }
            chromeBinaryOptional = findChromeExecutable();
            String chromeBinary = chromeBinaryOptional.get().toString();
            log.info("new chromeBinary:{}", chromeBinary);
            return chromeBinary;
        }
    }

    public static Optional<Path> findChromeExecutable() {
        Path chromeExeDir = Paths.get(PROJECT_HOME, "chrome");
        log.info("Searching for chrome executable in project home {}", chromeExeDir);
        // only support macos and linux
        PathMatcher matcher = FileSystems.getDefault().getPathMatcher(isMacOS() ? CHROME_MACOS_PATH : CHROME_LINUX_PATH);

        AtomicReference<Path> result = new AtomicReference<>();

        try {
            Files.walkFileTree(chromeExeDir, new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) {
                    if (matcher.matches(file)) {
                        result.set(file);
                        return FileVisitResult.TERMINATE;
                    }
                    return FileVisitResult.CONTINUE;
                }
            });
        } catch (IOException e) {
            log.error("Error while searching for chrome executable", e);
            return Optional.empty();
        }

        return Optional.ofNullable(result.get());
    }

    public static boolean isRightChromeVersion(Path chromePath, String chromeVersion) {
        ProcessBuilder processBuilder = new ProcessBuilder(
                chromePath.toString(),
                "--version"
        );
        log.info("Checking if chrome version: {}, in path: {}", chromeVersion, chromePath);
        try {
            Process process = processBuilder.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String versionInfo = reader.readLine();
            log.info("Chrome version info: {}.", versionInfo);
            
            if (versionInfo != null && versionInfo.contains(chromeVersion)) {
                return true;
            }

            int exitCode = process.waitFor();
            if (exitCode != 0) {
                log.error("Error: Chrome process exited with code " + exitCode);
            }
        } catch (Exception e) {
            log.error("Error checking Chrome version: " + e.getMessage());
        }

        return false;
    }

    public static boolean callChromeScript() {
        String scriptPath = Paths.get(PROJECT_HOME, CHROME_DOWNLOADER_SCRIPT).toString();
        log.info("/bin/bash {}", scriptPath);
        ProcessBuilder processBuilder = new ProcessBuilder("/bin/bash", scriptPath);
        processBuilder.directory(new File(PROJECT_HOME));

        try {
            processBuilder.redirectErrorStream(true);
            Process process = processBuilder.start();

            // Capture and print the script's output
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line = null;
            boolean isFirstLine = true;
            while ((line = reader.readLine()) != null) {
                if (isFirstLine) {
                    isFirstLine = false;
                    log.info("first time output");
                }
                log.info(line);
            }

            // Capture and print any error output
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            while ((line = errorReader.readLine()) != null) {
                log.error(line);
                line = null;
            }

            int exitCode = process.waitFor();
            if (exitCode == 0) {
                log.error("Chrome process successfully exited with code " + exitCode);
                return true;
            }
        } catch (Exception e) {
            log.error("Call chrome script error", e);
        }

        return false;
    }

    public static boolean isMacOS() {
        String os = System.getProperty("os.name").toLowerCase();
        log.info("OS: " + os);
        return os.contains("mac") || os.contains("darwin");
    }

}
