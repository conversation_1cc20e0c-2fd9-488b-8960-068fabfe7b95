package io.nue.pdf.controller;

import static com.ruby.commons.RubyException.UNEXPECTED_ERROR_MESSAGE;

import com.ruby.commons.ApiError;
import com.ruby.commons.ErrorIDException;
import com.ruby.commons.NoSessionException;
import com.ruby.commons.RubyException;

import io.nue.exception.NueException;
import io.nue.exception.NueValidationException;
import io.nue.model.exception.DataNotFoundException;
import io.nue.pdf.common.ErrorMessage;
import io.nue.pdf.filter.LoggingFilter;
import io.nue.util.log.LogUtils;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.util.stream.Collectors;

@ControllerAdvice
public class ControllerExceptionHandler extends ResponseEntityExceptionHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ControllerExceptionHandler.class);

    private static final String ERR_MSG_UNEXPECTED_ERROR = "Unexpected error, please contact system administrator.";
    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatusCode statusCode,
            WebRequest request) {
        BindingResult bindingResult = ex.getBindingResult();
        StringBuilder sb = new StringBuilder("Request Validation failed by: ");
        if (bindingResult.hasFieldErrors()) {
            sb.append(bindingResult.getFieldErrors().stream().map(fieldError -> {
                StringBuilder error = new StringBuilder();
                error.append('[')
                        .append(fieldError.getField())
                        .append(':')
                        .append(fieldError.getRejectedValue())
                        .append(", reason:")
                        .append(fieldError.getDefaultMessage())
                        .append(']');
                return error.toString();
            }).collect(Collectors.joining(",")));

        } else if (bindingResult.hasErrors()) {
            for (ObjectError error : bindingResult.getAllErrors()) {
                sb.append('[').append(error).append("] ");
            }
        }

        LOGGER.info("Request validation error: {}", sb.toString());
        return ResponseEntity.status(statusCode).body(new ApiError(statusCode.value(), HttpStatus.valueOf(statusCode.value()).getReasonPhrase(), sb.toString()));
    }

    @ExceptionHandler(value = {
            NueException.class
    })
    public ResponseEntity<Object> handleConflict(RuntimeException e, WebRequest request) {
        
        if(NueException.class.isInstance(e)){
            LOGGER.error("Caught Exception: ", e);
            NueException nueException = (NueException) e;
            return getResponseForError(HttpStatus.INTERNAL_SERVER_ERROR, "Oops! An error occurred", nueException.getErrorCode(), e.getMessage());
        }
        ErrorIDException errorIDException = new ErrorIDException(e);
        LogUtils.setMdcErrorId(errorIDException.getErrorId());
        LOGGER.error("Caught Exception: ", e);
    return getResponseForError(HttpStatus.INTERNAL_SERVER_ERROR, "UNEXPECTED_ERROR", errorIDException.getMessage());
    }
    
    @ExceptionHandler({
            RubyException.class,
    })
    @ResponseBody
    public ResponseEntity<ApiError> handleRubyException(HttpServletRequest request, Throwable ex) {

        HttpStatus status = getStatus(request);

        request.setAttribute(LoggingFilter.REQUEST_ATTR_ERROR, ex); //for logging
        ApiError error = new ApiError(status.value(), status.getReasonPhrase(), ex.getMessage());
        return new ResponseEntity<>(error, new HttpHeaders(), status);
    }

    /**
     * For exceptions not wrapped by {@link RubyException}, unexpected error message will be returned in case of disclosure system details.
     */
    @ExceptionHandler({
            Exception.class,
    })
    @ResponseBody
    public ResponseEntity<Object> handleUnexpectedException(HttpServletRequest request, Throwable ex) {
        HttpStatus status = getStatus(request);
        request.setAttribute(LoggingFilter.REQUEST_ATTR_ERROR, ex); //for logging
        return new ResponseEntity<>(new ApiError(status.value(), status.getReasonPhrase(), UNEXPECTED_ERROR_MESSAGE), status);
    }

    private HttpStatus getStatus(HttpServletRequest request) {
        Integer statusCode = (Integer) request.getAttribute("jakarta.servlet.error.status_code");
        if (statusCode == null) {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }
        return HttpStatus.valueOf(statusCode);
    }

    private ResponseEntity<Object> getResponseForError(HttpStatus status, String errorTitle, String errorMessage) {
        return getResponseForError(status, errorTitle, errorTitle, errorMessage);
    }

    private ResponseEntity<Object> getResponseForError(HttpStatus status, String errorTitle, String errorCode, String errorMessage) {
        if (status == null) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
        }
        if (errorTitle == null) {
            errorTitle = status.name();
        }
        if (errorCode == null) {
            errorCode = errorTitle;
        }
        if (errorMessage == null) {
            errorMessage = ERR_MSG_UNEXPECTED_ERROR;
        }
        return new ResponseEntity<>(new ErrorMessage(errorTitle, errorMessage, errorCode), status);
    }
}