package io.nue.pdf.controller;

import static org.springframework.http.HttpStatus.OK;

import io.nue.pdf.utils.ProcessMonitorUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
public class HealthController {

    @GetMapping(path = "/health")
    @ResponseStatus(OK)
    public void healthCheck() {

    }

    @GetMapping("/top-cpu")
    public List<Map<String, Object>> topCpu(@RequestParam(name = "topN", required = true, defaultValue = "10") int topN) {
        if (topN <= 0) {
            throw new IllegalArgumentException("{topN} should greater than 0");
        }
        return ProcessMonitorUtil.getTopCpuProcesses(topN);
    }

    @GetMapping("/top-memory")
    public List<Map<String, Object>> topMemory(@RequestParam(name = "topN", required = true, defaultValue = "10") int topN) {
        if (topN <= 0) {
            throw new IllegalArgumentException("topN should greater than 0");
        }
        return ProcessMonitorUtil.getTopMemoryProcesses(topN);
    }
}
