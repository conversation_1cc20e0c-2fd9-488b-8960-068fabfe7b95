package io.nue.pdf.controller;

import io.nue.exception.NueException;
import io.nue.pdf.service.InvoicePdfService;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@RestController
public class PdfController {

    private static final Logger log = LoggerFactory.getLogger(PdfController.class);

    public static final String DOWNLOADS_FOLDER = System.getProperty("user.home") + "/Downloads";
    private final InvoicePdfService invoicePdfService;
    private final String rootUrl;

    public PdfController(InvoicePdfService invoicePdfService, @Value("${app.root-url}") String rootUrl) {
        this.invoicePdfService = invoicePdfService;
        this.rootUrl = rootUrl;
    }

    @RequestMapping(value = "/magiclink-pdf/view-{formType}/**", method = RequestMethod.GET, produces = MediaType.APPLICATION_PDF_VALUE)
    public ResponseEntity<?> magiclinkPdf(@PathVariable("formType") String formType, HttpServletRequest request) {
        String magiclink = request.getRequestURI().substring("/magiclink-pdf".length());
        
        try {
            return invoicePdfService.magicklinkPdf(this.rootUrl + magiclink);
        } catch (NueException e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).body(e.getMessage());
        } catch (Exception e) {
            log.error("Error generating PDF for magiclink: " + magiclink, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

}
