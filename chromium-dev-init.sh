#!/bin/bash

# Set up directories
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
CHROMIUM_DIR="${SCRIPT_DIR}/chromium"

echo "Chromium directory: ${CHROMIUM_DIR}"
# Create chromium directory if it doesn't exist
mkdir -p "${CHROMIUM_DIR}"
EXPECTED_VERSION="126.0"

# Determine the OS and architecture
if [[ "$(uname)" == 'Linux' ]]; then
    if [[ "$(uname -m)" == 'arm64' ]]; then
        ARCH="linux-arm64"
    else
        ARCH="linux-x64"
    fi
elif [[ "$(uname)" == 'Darwin' ]]; then
    if [[ "$(uname -m)" == 'arm64' ]]; then
        ARCH="mac-arm64"
    else
        ARCH="mac-x64"
    fi
else
    echo "Unsupported operating system"
    exit 1
fi

echo "Detected architecture: ${ARCH}"

# Function to find Chromium executable
find_chromium() {
    if [[ "$(uname)" == 'Darwin' ]]; then
        # macOS
        find "${CHROMIUM_DIR}" -name "Chromium" -type f -perm +111 2>/dev/null
    elif [[ "$(uname)" == 'Linux' ]]; then
        # Linux
        find "${CHROMIUM_DIR}" -name "chromium" -type f -executable 2>/dev/null
    else
        echo "Unsupported operating system"
        return 1
    fi        
}

# Function to get Chromium version
get_chromium_version() {
  if [[ "$(uname)" == 'Darwin' || "$(uname)" == 'Linux' ]]; then
    "$1" --version | sed -E 's/Chromium ([0-9]+\.[0-9]+\.[0-9]+\.[0-9]+).*/\1/'
  else
    echo "Unsupported operating system"
    return 1
  fi
}

# Function to download and extract Chromium  xattr -d com.apple.quarantine Chromium.app
unzip_chromium() {
    ZIP_FILE="${SCRIPT_DIR}/chrome-mac-126.zip"
    # clear the directory if it exists
    echo "Clearing ${CHROMIUM_DIR}"
    rm -rf "${CHROMIUM_DIR}"/*
    # Extract the zip file
    echo "Extracting Chromium...${ZIP_FILE}"
    unzip -q -o "${ZIP_FILE}" -d "${CHROMIUM_DIR}"
    echo "Chromium has been extracted to "$(find_chromium)
    # Remove quarantine attribute from Chromium.app
    if xattr -p com.apple.quarantine "${CHROMIUM_DIR}"/chrome-mac/Chromium.app &>/dev/null; then
        xattr -d com.apple.quarantine "${CHROMIUM_DIR}"/chrome-mac/Chromium.app
    else
        echo "No quarantine attribute found. No action needed."
    fi
}

CHROMIUM_PATH=$(find_chromium)
echo "Chromium path: ${CHROMIUM_PATH}"
# Check if Chromium is already installed
if [ -z "${CHROMIUM_PATH}" ]; then
    echo "Chromium for Testing not found in ${CHROMIUM_DIR}"
    if [[ "$(uname)" == 'Darwin' ]]; then
      unzip_chromium
    fi
else
    CHROMIUM_VERSION=$(get_chromium_version "${CHROMIUM_PATH}")
    echo "Found Chromium version: ${CHROMIUM_VERSION}, expected ${EXPECTED_VERSION}."
    if [[ "${CHROMIUM_VERSION}" != ${EXPECTED_VERSION}* ]]; then
        echo "Clearing and downloading correct version."
        if [[ "$(uname)" == 'Darwin' || "$(uname)" == 'Linux' ]]; then
            unzip_chromium
        fi
    else
        echo "Chromium version is correct: ${CHROMIUM_VERSION}"
        exit 0
   fi
fi

CHROMIUM_PATH=$(find_chromium)
if [ -z "${CHROMIUM_PATH}" ]; then
    echo "Failed to install Chromium"
    exit 1
else
    CHROMIUM_VERSION=$(get_chromium_version "${CHROMIUM_PATH}")
    echo "Installed Chromium version: ${CHROMIUM_VERSION} for ${ARCH} has been extracted to ${CHROMIUM_DIR}"
    echo "End Suceessfully!"
fi