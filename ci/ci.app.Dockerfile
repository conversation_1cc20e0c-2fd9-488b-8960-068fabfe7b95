FROM registry.gitlab.com/nue-apps/nue-images/nue-base-image:latest as nue-otel-extension

RUN echo "pulling image to copy nue-telemetry extension"

FROM maven:3.9.8-amazoncorretto-21-al2023 as builder

ARG MVN_USERNAME
ARG MVN_PASSWORD
ARG GITLAB_PAT

COPY ./ci/ci.settings.xml /usr/share/maven/ref/settings.xml

WORKDIR /tmp
COPY ./.mvn /tmp/.mvn
COPY ./pom.xml /tmp/pom.xml
COPY ./pdf-application/pom.xml /tmp/pdf-application/pom.xml
COPY ./pdf-api-client/pom.xml /tmp/pdf-api-client/pom.xml

RUN --mount=type=cache,target=/root/.m2 mvn -f /tmp/pom.xml -s /usr/share/maven/ref/settings.xml dependency:go-offline -DexcludeReactor=true

COPY ./pdf-application/src /tmp/pdf-application/src


RUN --mount=type=cache,target=/root/.m2 mvn -B -f /tmp/pom.xml -s /usr/share/maven/ref/settings.xml clean package -Dbasepom.check.skip-extended=true -DskipTests -DoutputDirectory=/tmp

FROM selenium/standalone-chromium:126.0

ARG APP_NAME
ARG PORT
ENV APP_NAME=${APP_NAME}
ARG SERVICE_NAME
ARG ENVIRONMENT
ENV OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
ENV OTEL_TRACES_SAMPLER="NueConfigurableSamplerProvider"

RUN wget -q -O - https://download.bell-sw.com/pki/GPG-KEY-bellsoft | sudo apt-key add -
RUN echo "deb https://apt.bell-sw.com/ stable main" | sudo tee /etc/apt/sources.list.d/bellsoft.list
RUN sudo apt-get update
RUN sudo apt-get install bellsoft-java21-runtime

WORKDIR /app

COPY --from=builder ./tmp/pdf-application/target/${APP_NAME}.jar ./${APP_NAME}.jar
COPY --from=nue-otel-extension  ./otel /otel
COPY ./chromium-dev-init.sh /app/chromium-dev-init.sh

EXPOSE ${PORT}

CMD java -jar /app/${APP_NAME}.jar
