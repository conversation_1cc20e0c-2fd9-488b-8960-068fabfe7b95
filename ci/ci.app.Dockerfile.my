FROM nue-magiclink-to-pdf-base-app:127.0.6533.88

ARG PORT

# Create a new user
RUN groupadd -r chrome && useradd -r -g chrome -G audio,video chrome \
    && mkdir -p /home/<USER>/home/<USER>

WORKDIR /app

COPY ./target/nue-magiclink-to-pdf-1.0-SNAPSHOT.jar ./nue-magiclink-to-pdf-app.jar
COPY start.sh /app/start.sh

EXPOSE ${PORT}
# Change ownership of the application files
RUN chown -R chrome: chrome /app

# Switch to the new user
USER chrome

RUN chmod 755 /app/start.sh
CMD ["/app/start.sh"]

