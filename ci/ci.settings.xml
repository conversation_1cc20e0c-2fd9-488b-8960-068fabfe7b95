<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 https://maven.apache.org/xsd/settings-1.0.0.xsd">
    <servers>
        <server>
            <id>gitlab-maven</id>
            <configuration>
                <httpHeaders>
                <property>
                    <name>Private-Token</name>
                    <value>${GITLAB_PAT}</value>
                </property>
                </httpHeaders>
            </configuration>
        </server>
        <server>
            <id>releases</id>
            <username>${env.MVN_USERNAME}</username>
            <password>${env.MVN_PASSWORD}</password>
        </server>
        <server>
            <id>snapshots</id>
            <username>${env.MVN_USERNAME}</username>
            <password>${env.MVN_PASSWORD}</password>
        </server>
        <server>
            <id>nexus</id>
            <username>${env.MVN_USERNAME}</username>
            <password>${env.MVN_PASSWORD}</password>
        </server>
    </servers>
    <mirrors>
        <mirror>
            <id>nexus</id>
            <mirrorOf>central</mirrorOf>
            <url>https://nexus.nuedev.click/repository/maven-public/</url>
        </mirror>
    </mirrors>
    <profiles>
        <profile>
            <id>ruby</id>
            <repositories>
                <repository>
                    <id>gitlab-maven</id>
                    <name>Gitlab - Nue Packages</name>
                    <url>https://gitlab.com/api/v4/groups/56009489/-/packages/maven</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>snapshots</id>
                    <url>https://nexus.nuedev.click/repository/snapshots/</url>
                </repository>
                <repository>
                    <id>releases</id>
                    <url>https://nexus.nuedev.click/repository/releases/</url>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>snapshots</id>
                    <url>https://nexus.nuedev.click/repository/snapshots/</url>
                </pluginRepository>
                <pluginRepository>
                    <id>releases</id>
                    <url>https://nexus.nuedev.click/repository/releases/</url>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>
    <activeProfiles>
        <activeProfile>ruby</activeProfile>
    </activeProfiles>
</settings>
